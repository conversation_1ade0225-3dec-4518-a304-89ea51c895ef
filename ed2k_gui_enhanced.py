#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ED2K 批量处理工具 - 增强版
现代化macOS/iOS风格界面设计
支持液态玻璃效果、平滑动画、进度指示器等现代UI特性

作者: AI Assistant
版本: 4.0.0 Enhanced
"""

import os
import re
import xml.etree.ElementTree as ET
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from tkinter.scrolledtext import ScrolledText
import urllib.parse
from pathlib import Path
import threading
import time
from typing import List, Tuple, Dict
import json

APP_VERSION = "4.0.0 Enhanced"
APP_NAME = "ED2K 批量处理工具"
VIDEO_EXTS = {'.mp4', '.mkv', '.avi', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.3gp', '.ts', '.mts'}
AUDIO_EXTS = {'.mp3', '.flac', '.wav', '.aac', '.ogg', '.m4a', '.wma'}
ARCHIVE_EXTS = {'.zip', '.rar', '.7z', '.tar', '.gz', '.bz2'}
AD_PATTERN = re.compile(r"【更多.*?】|更多无水印.*?】|更多无水印.*?\.|www\.[^\s]+|BBQDDQ|BPHDTV", re.IGNORECASE)

# 配置文件路径
CONFIG_FILE = "ed2k_config.json"

# macOS/iOS 风格主题配置
class ModernTheme:
    # 液态玻璃效果颜色 - iOS 16 风格
    GLASS_BG = "#F2F2F7"  # iOS 系统背景色
    GLASS_SECONDARY = "#FFFFFF"  # 卡片背景
    GLASS_TERTIARY = "#F2F2F7"  # 三级背景
    GLASS_OVERLAY = "#FFFFFF80"  # 半透明覆盖层
    
    # 文字颜色 - iOS 语义化颜色
    TEXT_PRIMARY = "#000000"  # 主要文字
    TEXT_SECONDARY = "#3C3C43"  # 次要文字
    TEXT_TERTIARY = "#8E8E93"  # 三级文字
    TEXT_QUATERNARY = "#C7C7CC"  # 四级文字
    
    # 系统颜色 - iOS 16 系统色彩
    BLUE = "#007AFF"  # 系统蓝色
    GREEN = "#34C759"  # 系统绿色
    RED = "#FF3B30"  # 系统红色
    ORANGE = "#FF9500"  # 系统橙色
    PURPLE = "#AF52DE"  # 系统紫色
    CYAN = "#5AC8FA"  # 系统青色
    YELLOW = "#FFCC00"  # 系统黄色
    PINK = "#FF2D92"  # 系统粉色
    
    # 渐变色配置
    GRADIENT_BLUE = ["#007AFF", "#5AC8FA"]
    GRADIENT_GREEN = ["#34C759", "#30D158"]
    GRADIENT_RED = ["#FF3B30", "#FF453A"]
    GRADIENT_PURPLE = ["#AF52DE", "#BF5AF2"]
    
    # 字体配置 - SF Pro 字体系列
    FONT_LARGE_TITLE = ("SF Pro Display", 28, "bold")
    FONT_TITLE1 = ("SF Pro Display", 22, "bold")
    FONT_TITLE2 = ("SF Pro Display", 17, "bold")
    FONT_TITLE3 = ("SF Pro Display", 15, "bold")
    FONT_HEADLINE = ("SF Pro Text", 14, "bold")
    FONT_BODY = ("SF Pro Text", 14, "normal")
    FONT_CALLOUT = ("SF Pro Text", 13, "normal")
    FONT_SUBHEAD = ("SF Pro Text", 12, "normal")
    FONT_FOOTNOTE = ("SF Pro Text", 11, "normal")
    FONT_CAPTION1 = ("SF Pro Text", 10, "normal")
    FONT_CAPTION2 = ("SF Pro Text", 9, "normal")
    FONT_MONO = ("SF Mono", 11, "normal")
    
    # 间距配置
    PADDING_LARGE = 24
    PADDING_MEDIUM = 16
    PADDING_SMALL = 12
    PADDING_TINY = 8
    
    # 圆角配置
    CORNER_RADIUS_LARGE = 16
    CORNER_RADIUS_MEDIUM = 12
    CORNER_RADIUS_SMALL = 8
    CORNER_RADIUS_TINY = 4

class AppConfig:
    """应用配置管理"""
    def __init__(self):
        self.config = self.load_config()
    
    def load_config(self) -> dict:
        """加载配置文件"""
        default_config = {
            "theme": "auto",  # auto, light, dark
            "window_geometry": "900x700",
            "auto_save_results": True,
            "show_file_count": True,
            "enable_animations": True,
            "check_updates": True,
            "language": "zh_CN",
            "recent_folders": [],
            "custom_video_exts": [],
            "custom_audio_exts": [],
            "exclude_patterns": []
        }
        
        try:
            if os.path.exists(CONFIG_FILE):
                with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    default_config.update(loaded_config)
        except Exception as e:
            print(f"加载配置文件失败: {e}")
        
        return default_config
    
    def save_config(self):
        """保存配置文件"""
        try:
            with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def get(self, key: str, default=None):
        """获取配置项"""
        return self.config.get(key, default)
    
    def set(self, key: str, value):
        """设置配置项"""
        self.config[key] = value
        self.save_config()

class AnimationHelper:
    """动画辅助类"""
    @staticmethod
    def fade_in(widget, duration=300, steps=20):
        """淡入动画"""
        step_time = duration // steps
        alpha_step = 1.0 / steps
        
        def animate(step=0):
            if step <= steps:
                alpha = step * alpha_step
                # 这里可以实现透明度动画，但tkinter原生不支持
                # 可以通过改变颜色来模拟
                widget.update()
                widget.after(step_time, lambda: animate(step + 1))
        
        animate()
    
    @staticmethod
    def slide_in(widget, direction="left", duration=300):
        """滑入动画"""
        # tkinter原生不支持复杂动画，这里提供接口
        pass
    
    @staticmethod
    def bounce_effect(widget):
        """弹跳效果"""
        original_relief = widget.cget("relief")
        widget.config(relief=tk.RAISED)
        widget.after(100, lambda: widget.config(relief=original_relief))

class FileTypeAnalyzer:
    """文件类型分析器"""
    @staticmethod
    def get_file_category(filename: str) -> str:
        """获取文件类别"""
        ext = os.path.splitext(filename)[1].lower()
        
        if ext in VIDEO_EXTS:
            return "video"
        elif ext in AUDIO_EXTS:
            return "audio"
        elif ext in ARCHIVE_EXTS:
            return "archive"
        elif ext in {'.txt', '.doc', '.docx', '.pdf', '.rtf'}:
            return "document"
        elif ext in {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'}:
            return "image"
        else:
            return "other"
    
    @staticmethod
    def get_file_icon(category: str) -> str:
        """获取文件类型图标"""
        icons = {
            "video": "🎬",
            "audio": "🎵",
            "archive": "📦",
            "document": "📄",
            "image": "🖼️",
            "other": "📁"
        }
        return icons.get(category, "📁")
    
    @staticmethod
    def format_file_size(size_str: str) -> str:
        """格式化文件大小"""
        try:
            size = int(size_str)
            for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
                if size < 1024.0:
                    return f"{size:.1f} {unit}"
                size /= 1024.0
            return f"{size:.1f} PB"
        except:
            return size_str

class ModernTooltip:
    """现代化工具提示"""
    def __init__(self, widget, text):
        self.widget = widget
        self.text = text
        self.tooltip = None
        
        widget.bind("<Enter>", self.show_tooltip)
        widget.bind("<Leave>", self.hide_tooltip)
    
    def show_tooltip(self, event=None):
        """显示工具提示"""
        if self.tooltip:
            return
        
        x = self.widget.winfo_rootx() + 25
        y = self.widget.winfo_rooty() + 25
        
        self.tooltip = tk.Toplevel(self.widget)
        self.tooltip.wm_overrideredirect(True)
        self.tooltip.wm_geometry(f"+{x}+{y}")
        
        label = tk.Label(
            self.tooltip,
            text=self.text,
            font=ModernTheme.FONT_CAPTION1,
            bg="#2C2C2E",
            fg="white",
            padx=8,
            pady=4,
            relief=tk.FLAT
        )
        label.pack()
    
    def hide_tooltip(self, event=None):
        """隐藏工具提示"""
        if self.tooltip:
            self.tooltip.destroy()
            self.tooltip = None

# 导入原有的类和功能
# 这里会继续使用之前定义的 ModernButton, ProgressDialog, Ed2kBatchApp 等类
