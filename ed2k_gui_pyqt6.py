#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ED2K 批量处理工具 - PyQt6现代化版本
采用现代化设计语言，支持液态玻璃效果、emoji图标、合理的键位布局

作者: AI Assistant
版本: 5.0.0 PyQt6
"""

import sys
import os
import re
import xml.etree.ElementTree as ET
import urllib.parse
from pathlib import Path
import threading
import json
from typing import List, Tuple, Dict, Optional

from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QGridLayout, QTextEdit, QPlainTextEdit, QPushButton, QLabel, 
    QFrame, QScrollArea, QProgressBar, QMessageBox, QDialog,
    QSplitter, QTabWidget, QGroupBox, QStatusBar, QMenuBar,
    QFileDialog, QCheckBox, QSpinBox, QComboBox
)
from PyQt6.QtCore import (
    Qt, QThread, pyqtSignal, QTimer, QPropertyAnimation, 
    QEasingCurve, QRect, QSize, QPoint
)
from PyQt6.QtGui import (
    QFont, QColor, QPalette, QPixmap, QPainter, QBrush, 
    QLinearGradient, QIcon, QAction, QKeySequence, QTextCursor
)

APP_VERSION = "5.0.0"
APP_NAME = "ED2K 批量处理工具"
VIDEO_EXTS = {'.mp4', '.mkv', '.avi', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.3gp', '.ts', '.mts'}
AUDIO_EXTS = {'.mp3', '.flac', '.wav', '.aac', '.ogg', '.m4a', '.wma'}
ARCHIVE_EXTS = {'.zip', '.rar', '.7z', '.tar', '.gz', '.bz2'}
AD_PATTERN = re.compile(r"【更多.*?】|更多无水印.*?】|更多无水印.*?\.|www\.[^\s]+|BBQDDQ|BPHDTV", re.IGNORECASE)

class ModernTheme:
    """现代化主题配置 - iOS/macOS风格"""
    
    # 主色调 - 液态玻璃效果
    PRIMARY_BG = "#F2F2F7"          # 主背景
    CARD_BG = "#FFFFFF"             # 卡片背景
    SECONDARY_BG = "#F8F9FA"        # 次要背景
    ACCENT_BG = "#E5E5EA"           # 强调背景
    
    # 文字颜色
    TEXT_PRIMARY = "#1D1D1F"        # 主要文字
    TEXT_SECONDARY = "#86868B"      # 次要文字
    TEXT_TERTIARY = "#C7C7CC"       # 三级文字
    TEXT_LINK = "#007AFF"           # 链接文字
    
    # 系统颜色
    BLUE = "#007AFF"                # 系统蓝
    GREEN = "#34C759"               # 系统绿
    RED = "#FF3B30"                 # 系统红
    ORANGE = "#FF9500"              # 系统橙
    PURPLE = "#AF52DE"              # 系统紫
    CYAN = "#5AC8FA"                # 系统青
    YELLOW = "#FFCC00"              # 系统黄
    PINK = "#FF2D92"                # 系统粉
    
    # 渐变色
    GRADIENT_BLUE = ["#007AFF", "#5AC8FA"]
    GRADIENT_GREEN = ["#34C759", "#30D158"]
    GRADIENT_RED = ["#FF3B30", "#FF453A"]
    
    # 字体配置
    FONT_FAMILY = "SF Pro Display, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif"
    FONT_SIZE_LARGE = 24
    FONT_SIZE_TITLE = 18
    FONT_SIZE_BODY = 14
    FONT_SIZE_CAPTION = 12
    FONT_SIZE_SMALL = 10
    
    # 间距配置
    SPACING_LARGE = 24
    SPACING_MEDIUM = 16
    SPACING_SMALL = 12
    SPACING_TINY = 8
    
    # 圆角配置
    BORDER_RADIUS = 12
    BUTTON_RADIUS = 8
    CARD_RADIUS = 16

class ModernButton(QPushButton):
    """现代化按钮组件"""
    
    def __init__(self, text: str = "", icon_emoji: str = "", style: str = "primary", parent=None):
        super().__init__(parent)
        self.icon_emoji = icon_emoji
        self.button_style = style
        self.setup_button(text)
        self.setup_style()
        self.setup_animations()
    
    def setup_button(self, text: str):
        """设置按钮基本属性"""
        display_text = f"{self.icon_emoji} {text}" if self.icon_emoji else text
        self.setText(display_text)
        self.setMinimumHeight(44)  # iOS标准触摸目标大小
        self.setFont(QFont(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_BODY, QFont.Weight.Medium))
        self.setCursor(Qt.CursorShape.PointingHandCursor)
    
    def setup_style(self):
        """设置按钮样式"""
        styles = {
            "primary": {
                "bg": ModernTheme.BLUE,
                "hover": "#0056CC",
                "text": "#FFFFFF"
            },
            "success": {
                "bg": ModernTheme.GREEN,
                "hover": "#28A745",
                "text": "#FFFFFF"
            },
            "danger": {
                "bg": ModernTheme.RED,
                "hover": "#DC3545",
                "text": "#FFFFFF"
            },
            "warning": {
                "bg": ModernTheme.ORANGE,
                "hover": "#E8890B",
                "text": "#FFFFFF"
            },
            "info": {
                "bg": ModernTheme.CYAN,
                "hover": "#17A2B8",
                "text": "#FFFFFF"
            },
            "secondary": {
                "bg": ModernTheme.PURPLE,
                "hover": "#9A42C8",
                "text": "#FFFFFF"
            }
        }
        
        style_config = styles.get(self.button_style, styles["primary"])
        
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {style_config['bg']};
                color: {style_config['text']};
                border: none;
                border-radius: {ModernTheme.BUTTON_RADIUS}px;
                padding: 12px 24px;
                font-weight: 500;
                font-size: {ModernTheme.FONT_SIZE_BODY}px;
            }}
            QPushButton:hover {{
                background-color: {style_config['hover']};
            }}
            QPushButton:pressed {{
                background-color: {style_config['hover']};
            }}
            QPushButton:disabled {{
                background-color: {ModernTheme.TEXT_TERTIARY};
                color: {ModernTheme.TEXT_SECONDARY};
            }}
        """)
    
    def setup_animations(self):
        """设置按钮动画"""
        self.animation = QPropertyAnimation(self, b"geometry")
        self.animation.setDuration(150)
        self.animation.setEasingCurve(QEasingCurve.Type.OutCubic)

class ModernCard(QFrame):
    """现代化卡片组件"""

    def __init__(self, title: str = "", parent=None):
        super().__init__(parent)
        self.setup_card()
        self.main_layout = QVBoxLayout(self)
        if title:
            self.add_title(title)

    def setup_card(self):
        """设置卡片样式"""
        self.setFrameStyle(QFrame.Shape.NoFrame)
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {ModernTheme.CARD_BG};
                border-radius: {ModernTheme.CARD_RADIUS}px;
                border: 1px solid {ModernTheme.ACCENT_BG};
            }}
        """)

        # 添加阴影效果（通过边框模拟）
        self.setContentsMargins(2, 2, 2, 2)

    def add_title(self, title: str):
        """添加卡片标题"""
        title_label = QLabel(title)
        title_label.setFont(QFont(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_TITLE, QFont.Weight.Bold))
        title_label.setStyleSheet(f"color: {ModernTheme.TEXT_PRIMARY}; margin-bottom: 12px;")
        self.main_layout.addWidget(title_label)

    def get_layout(self):
        """获取主布局"""
        return self.main_layout

class ProcessingDialog(QDialog):
    """现代化处理进度对话框"""
    
    def __init__(self, title: str = "处理中", message: str = "请稍候...", parent=None):
        super().__init__(parent)
        self.setup_dialog(title, message)
    
    def setup_dialog(self, title: str, message: str):
        """设置对话框"""
        self.setWindowTitle(title)
        self.setFixedSize(400, 200)
        self.setModal(True)
        self.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.FramelessWindowHint)
        
        # 设置样式
        self.setStyleSheet(f"""
            QDialog {{
                background-color: {ModernTheme.CARD_BG};
                border-radius: {ModernTheme.CARD_RADIUS}px;
                border: 1px solid {ModernTheme.ACCENT_BG};
            }}
        """)
        
        # 创建布局
        layout = QVBoxLayout(self)
        layout.setSpacing(ModernTheme.SPACING_MEDIUM)
        layout.setContentsMargins(ModernTheme.SPACING_LARGE, ModernTheme.SPACING_LARGE, 
                                 ModernTheme.SPACING_LARGE, ModernTheme.SPACING_LARGE)
        
        # 标题
        title_label = QLabel(f"⚡ {title}")
        title_label.setFont(QFont(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_TITLE, QFont.Weight.Bold))
        title_label.setStyleSheet(f"color: {ModernTheme.TEXT_PRIMARY};")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 消息
        self.message_label = QLabel(message)
        self.message_label.setFont(QFont(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_BODY))
        self.message_label.setStyleSheet(f"color: {ModernTheme.TEXT_SECONDARY};")
        self.message_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.message_label.setWordWrap(True)
        layout.addWidget(self.message_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 0)  # 无限进度条
        self.progress_bar.setStyleSheet(f"""
            QProgressBar {{
                border: none;
                border-radius: 6px;
                background-color: {ModernTheme.ACCENT_BG};
                height: 12px;
            }}
            QProgressBar::chunk {{
                border-radius: 6px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {ModernTheme.BLUE}, stop:1 {ModernTheme.CYAN});
            }}
        """)
        layout.addWidget(self.progress_bar)
        
        # 状态标签
        self.status_label = QLabel("")
        self.status_label.setFont(QFont(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_CAPTION))
        self.status_label.setStyleSheet(f"color: {ModernTheme.TEXT_TERTIARY};")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.status_label)
    
    def update_message(self, message: str):
        """更新消息"""
        self.message_label.setText(message)
    
    def update_status(self, status: str):
        """更新状态"""
        self.status_label.setText(status)
    
    def set_progress(self, value: int, maximum: int = 100):
        """设置进度"""
        self.progress_bar.setRange(0, maximum)
        self.progress_bar.setValue(value)

class WorkerThread(QThread):
    """工作线程基类"""
    
    progress_updated = pyqtSignal(int, int)  # 当前进度, 总数
    status_updated = pyqtSignal(str)         # 状态消息
    finished_signal = pyqtSignal(object)     # 完成信号
    error_signal = pyqtSignal(str)           # 错误信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.is_cancelled = False
    
    def cancel(self):
        """取消操作"""
        self.is_cancelled = True

class TextProcessWorker(WorkerThread):
    """文本处理工作线程"""
    
    def __init__(self, text: str, parent=None):
        super().__init__(parent)
        self.text = text
    
    def run(self):
        """执行文本处理"""
        try:
            lines = self.text.splitlines()
            video_links, other_links = [], []
            total_lines = len(lines)
            
            self.status_updated.emit("正在解析链接...")
            
            for i, line in enumerate(lines):
                if self.is_cancelled:
                    return
                
                line = line.strip()
                if not line or not line.startswith('ed2k://'):
                    continue
                
                if self.is_ad_ed2k(line):
                    continue
                
                try:
                    parts = line.split('|')
                    if len(parts) >= 5 and parts[1] == 'file':
                        filename = urllib.parse.unquote(parts[2])
                        ext = os.path.splitext(filename)[1].lower()
                        if ext in VIDEO_EXTS:
                            video_links.append(line)
                        else:
                            other_links.append(line)
                except Exception:
                    continue
                
                # 更新进度
                if i % 10 == 0:
                    self.progress_updated.emit(i, total_lines)
                    self.status_updated.emit(f"已处理 {i}/{total_lines} 行")
            
            self.progress_updated.emit(total_lines, total_lines)
            self.finished_signal.emit((video_links, other_links))
            
        except Exception as e:
            self.error_signal.emit(f"处理失败: {str(e)}")
    
    def is_ad_ed2k(self, ed2k_link: str) -> bool:
        """检查是否为广告链接"""
        try:
            parts = ed2k_link.split('|')
            if len(parts) >= 3:
                filename = urllib.parse.unquote(parts[2])
                return bool(AD_PATTERN.search(filename))
        except Exception:
            return False
        return False

class XMLProcessWorker(WorkerThread):
    """XML处理工作线程"""

    def __init__(self, parent=None):
        super().__init__(parent)

    def run(self):
        """执行XML处理"""
        try:
            folder = os.path.dirname(os.path.abspath(__file__))
            xml_files = list(Path(folder).glob('**/*.xml'))

            if not xml_files:
                self.error_signal.emit("当前目录下没有找到XML文件")
                return

            self.status_updated.emit(f"找到 {len(xml_files)} 个XML文件")
            xml_file_links = {}

            for i, xml_file in enumerate(xml_files):
                if self.is_cancelled:
                    return

                try:
                    self.status_updated.emit(f"处理 {xml_file.name}")
                    tree = ET.parse(xml_file)
                    root = tree.getroot()
                    file_list = root.find('FileList')
                    v_links, o_links = [], []

                    if file_list is not None:
                        for file_entry in file_list.findall('FileEntry'):
                            relative_path_name = file_entry.get('RelativePathName')
                            if not relative_path_name:
                                continue

                            if self.is_ad_filename(relative_path_name):
                                continue

                            ext = os.path.splitext(relative_path_name)[1].lower()
                            size = file_entry.get('Size')
                            e_mule_hash = file_entry.get('eMuleHash')

                            if not all([relative_path_name, size, e_mule_hash]):
                                continue

                            if relative_path_name.lower().startswith('ed2k:'):
                                continue

                            cleaned_name = self.clean_filename(relative_path_name)
                            e_mule_hash = e_mule_hash.upper()
                            encoded_filename = urllib.parse.quote(cleaned_name)
                            ed2k_link = f'ed2k://|file|{encoded_filename}|{size}|{e_mule_hash}|/'

                            if ext in VIDEO_EXTS:
                                v_links.append(ed2k_link)
                            else:
                                o_links.append(ed2k_link)

                    xml_file_links[xml_file.name] = [v_links, o_links]
                    self.progress_updated.emit(i + 1, len(xml_files))

                except Exception as e:
                    print(f"处理文件 {xml_file.name} 时出错: {e}")
                    continue

            self.finished_signal.emit(xml_file_links)

        except Exception as e:
            self.error_signal.emit(f"处理XML目录失败: {str(e)}")

    def clean_filename(self, filename: str) -> str:
        """清理文件名"""
        return AD_PATTERN.sub("", filename).strip()

    def is_ad_filename(self, filename: str) -> bool:
        """检查文件名是否包含广告"""
        return bool(AD_PATTERN.search(filename))

class ED2KMainWindow(QMainWindow):
    """主窗口类"""

    def __init__(self):
        super().__init__()
        self.video_links: List[str] = []
        self.other_links: List[str] = []
        self.xml_file_links: Dict[str, Tuple[List[str], List[str]]] = {}
        self.current_worker: Optional[WorkerThread] = None
        self.processing_dialog: Optional[ProcessingDialog] = None

        self.setup_ui()
        self.setup_shortcuts()
        self.setup_style()

    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle(f"{APP_NAME} v{APP_VERSION}")
        self.setMinimumSize(1000, 700)
        self.resize(1200, 800)

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(ModernTheme.SPACING_MEDIUM)
        main_layout.setContentsMargins(ModernTheme.SPACING_LARGE, ModernTheme.SPACING_LARGE,
                                      ModernTheme.SPACING_LARGE, ModernTheme.SPACING_LARGE)

        # 创建标题区域
        self.create_header(main_layout)

        # 创建主要内容区域
        self.create_main_content(main_layout)

        # 创建状态栏
        self.create_status_bar()

    def create_header(self, parent_layout):
        """创建标题区域"""
        header_layout = QHBoxLayout()

        # 应用标题
        title_label = QLabel(f"🚀 {APP_NAME}")
        title_label.setFont(QFont(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_LARGE, QFont.Weight.Bold))
        title_label.setStyleSheet(f"color: {ModernTheme.TEXT_PRIMARY};")
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # 版本信息
        version_label = QLabel(f"v{APP_VERSION}")
        version_label.setFont(QFont(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_CAPTION))
        version_label.setStyleSheet(f"color: {ModernTheme.TEXT_SECONDARY};")
        header_layout.addWidget(version_label)

        parent_layout.addLayout(header_layout)

    def create_main_content(self, parent_layout):
        """创建主要内容区域"""
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Vertical)

        # 上半部分：输入和操作区域
        top_widget = self.create_input_section()
        splitter.addWidget(top_widget)

        # 下半部分：结果显示区域
        bottom_widget = self.create_result_section()
        splitter.addWidget(bottom_widget)

        # 设置分割比例
        splitter.setSizes([300, 400])
        splitter.setChildrenCollapsible(False)

        parent_layout.addWidget(splitter)

    def create_input_section(self) -> QWidget:
        """创建输入区域"""
        # 创建输入卡片
        input_card = ModernCard("📝 输入区域")
        card_layout = input_card.get_layout()
        card_layout.setContentsMargins(ModernTheme.SPACING_MEDIUM, ModernTheme.SPACING_MEDIUM,
                                      ModernTheme.SPACING_MEDIUM, ModernTheme.SPACING_MEDIUM)

        # 输入提示
        hint_label = QLabel("💡 请在下方输入框中粘贴 ED2K 链接，支持批量处理")
        hint_label.setFont(QFont(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_CAPTION))
        hint_label.setStyleSheet(f"color: {ModernTheme.TEXT_SECONDARY}; margin-bottom: 8px;")
        card_layout.addWidget(hint_label)

        # 输入文本框
        self.input_text = QPlainTextEdit()
        self.input_text.setPlaceholderText("在此粘贴 ED2K 链接，每行一个...")
        self.input_text.setFont(QFont("Consolas, Monaco, monospace", ModernTheme.FONT_SIZE_BODY))
        self.input_text.setStyleSheet(f"""
            QPlainTextEdit {{
                background-color: {ModernTheme.SECONDARY_BG};
                border: 2px solid {ModernTheme.ACCENT_BG};
                border-radius: {ModernTheme.BUTTON_RADIUS}px;
                padding: 12px;
                color: {ModernTheme.TEXT_PRIMARY};
                selection-background-color: {ModernTheme.BLUE};
                selection-color: white;
            }}
            QPlainTextEdit:focus {{
                border-color: {ModernTheme.BLUE};
            }}
        """)
        self.input_text.setMinimumHeight(120)
        card_layout.addWidget(self.input_text)

        # 操作按钮区域
        self.create_action_buttons(card_layout)

        return input_card

    def create_action_buttons(self, parent_layout):
        """创建操作按钮"""
        # 主要操作按钮（第一行）
        main_buttons_layout = QHBoxLayout()
        main_buttons_layout.setSpacing(ModernTheme.SPACING_SMALL)

        self.convert_btn = ModernButton("转换文本", "🔄", "primary")
        self.convert_btn.clicked.connect(self.process_input)
        main_buttons_layout.addWidget(self.convert_btn)

        self.process_xml_btn = ModernButton("处理XML目录", "📁", "info")
        self.process_xml_btn.clicked.connect(self.process_folder)
        main_buttons_layout.addWidget(self.process_xml_btn)

        self.clear_xml_btn = ModernButton("清除XML", "🗑️", "danger")
        self.clear_xml_btn.clicked.connect(self.clear_xml_files)
        main_buttons_layout.addWidget(self.clear_xml_btn)

        main_buttons_layout.addStretch()
        parent_layout.addLayout(main_buttons_layout)

        # 复制操作按钮（第二行）
        copy_buttons_layout = QHBoxLayout()
        copy_buttons_layout.setSpacing(ModernTheme.SPACING_SMALL)

        self.copy_video_btn = ModernButton("复制视频链接", "🎬", "success")
        self.copy_video_btn.clicked.connect(self.copy_video_links)
        copy_buttons_layout.addWidget(self.copy_video_btn)

        self.copy_all_btn = ModernButton("复制全部链接", "📋", "secondary")
        self.copy_all_btn.clicked.connect(self.copy_all_links)
        copy_buttons_layout.addWidget(self.copy_all_btn)

        # 统计信息标签
        self.stats_label = QLabel("📊 等待处理...")
        self.stats_label.setFont(QFont(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_CAPTION))
        self.stats_label.setStyleSheet(f"color: {ModernTheme.TEXT_SECONDARY};")
        copy_buttons_layout.addWidget(self.stats_label)

        copy_buttons_layout.addStretch()
        parent_layout.addLayout(copy_buttons_layout)

    def create_result_section(self) -> QWidget:
        """创建结果显示区域"""
        # 创建结果卡片
        result_card = ModernCard("📄 转换结果")
        card_layout = result_card.get_layout()
        card_layout.setContentsMargins(ModernTheme.SPACING_MEDIUM, ModernTheme.SPACING_MEDIUM,
                                      ModernTheme.SPACING_MEDIUM, ModernTheme.SPACING_MEDIUM)

        # 创建标签页
        self.result_tabs = QTabWidget()
        self.result_tabs.setStyleSheet(f"""
            QTabWidget::pane {{
                border: 2px solid {ModernTheme.ACCENT_BG};
                border-radius: {ModernTheme.BUTTON_RADIUS}px;
                background-color: {ModernTheme.SECONDARY_BG};
            }}
            QTabBar::tab {{
                background-color: {ModernTheme.ACCENT_BG};
                color: {ModernTheme.TEXT_SECONDARY};
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: {ModernTheme.BUTTON_RADIUS}px;
                border-top-right-radius: {ModernTheme.BUTTON_RADIUS}px;
            }}
            QTabBar::tab:selected {{
                background-color: {ModernTheme.BLUE};
                color: white;
            }}
            QTabBar::tab:hover {{
                background-color: {ModernTheme.TEXT_TERTIARY};
            }}
        """)

        # 文本转换结果标签页
        self.text_result_tab = self.create_text_result_tab()
        self.result_tabs.addTab(self.text_result_tab, "🔄 文本转换")

        # XML处理结果标签页
        self.xml_result_tab = self.create_xml_result_tab()
        self.result_tabs.addTab(self.xml_result_tab, "📁 XML处理")

        card_layout.addWidget(self.result_tabs)
        return result_card

    def create_text_result_tab(self) -> QWidget:
        """创建文本结果标签页"""
        tab_widget = QWidget()
        layout = QVBoxLayout(tab_widget)

        # 结果显示文本框
        self.text_result = QTextEdit()
        self.text_result.setReadOnly(True)
        self.text_result.setFont(QFont("Consolas, Monaco, monospace", ModernTheme.FONT_SIZE_BODY))
        self.text_result.setStyleSheet(f"""
            QTextEdit {{
                background-color: white;
                border: 1px solid {ModernTheme.ACCENT_BG};
                border-radius: {ModernTheme.BUTTON_RADIUS}px;
                padding: 12px;
                color: {ModernTheme.TEXT_PRIMARY};
                selection-background-color: {ModernTheme.BLUE};
                selection-color: white;
            }}
        """)
        self.text_result.setPlaceholderText("转换结果将在这里显示...")
        layout.addWidget(self.text_result)

        return tab_widget

    def create_xml_result_tab(self) -> QWidget:
        """创建XML结果标签页"""
        tab_widget = QWidget()
        layout = QVBoxLayout(tab_widget)

        # XML结果显示文本框
        self.xml_result = QTextEdit()
        self.xml_result.setReadOnly(True)
        self.xml_result.setFont(QFont("Consolas, Monaco, monospace", ModernTheme.FONT_SIZE_BODY))
        self.xml_result.setStyleSheet(f"""
            QTextEdit {{
                background-color: white;
                border: 1px solid {ModernTheme.ACCENT_BG};
                border-radius: {ModernTheme.BUTTON_RADIUS}px;
                padding: 12px;
                color: {ModernTheme.TEXT_PRIMARY};
                selection-background-color: {ModernTheme.BLUE};
                selection-color: white;
            }}
        """)
        self.xml_result.setPlaceholderText("XML处理结果将在这里显示...")
        layout.addWidget(self.xml_result)

        return tab_widget

    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        # 状态标签
        self.status_label = QLabel("🟢 准备就绪")
        self.status_label.setFont(QFont(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_CAPTION))
        self.status_bar.addWidget(self.status_label)

        # 添加永久部件
        self.status_bar.addPermanentWidget(QLabel(f"© 2024 {APP_NAME}"))

        # 设置状态栏样式
        self.status_bar.setStyleSheet(f"""
            QStatusBar {{
                background-color: {ModernTheme.ACCENT_BG};
                color: {ModernTheme.TEXT_SECONDARY};
                border-top: 1px solid {ModernTheme.TEXT_TERTIARY};
            }}
        """)

    def setup_shortcuts(self):
        """设置快捷键"""
        # Ctrl+Enter: 转换文本
        convert_shortcut = QAction(self)
        convert_shortcut.setShortcut(QKeySequence("Ctrl+Return"))
        convert_shortcut.triggered.connect(self.process_input)
        self.addAction(convert_shortcut)

        # Ctrl+O: 处理XML目录
        xml_shortcut = QAction(self)
        xml_shortcut.setShortcut(QKeySequence("Ctrl+O"))
        xml_shortcut.triggered.connect(self.process_folder)
        self.addAction(xml_shortcut)

        # Ctrl+Shift+V: 复制视频链接
        copy_video_shortcut = QAction(self)
        copy_video_shortcut.setShortcut(QKeySequence("Ctrl+Shift+V"))
        copy_video_shortcut.triggered.connect(self.copy_video_links)
        self.addAction(copy_video_shortcut)

        # Ctrl+Shift+A: 复制全部链接
        copy_all_shortcut = QAction(self)
        copy_all_shortcut.setShortcut(QKeySequence("Ctrl+Shift+A"))
        copy_all_shortcut.triggered.connect(self.copy_all_links)
        self.addAction(copy_all_shortcut)

    def setup_style(self):
        """设置应用样式"""
        self.setStyleSheet(f"""
            QMainWindow {{
                background-color: {ModernTheme.PRIMARY_BG};
            }}
            QWidget {{
                font-family: {ModernTheme.FONT_FAMILY};
            }}
        """)

    def process_input(self):
        """处理输入文本"""
        text = self.input_text.toPlainText().strip()
        if not text:
            self.show_message("⚠️ 请输入要处理的链接", "warning")
            return

        if self.current_worker and self.current_worker.isRunning():
            self.show_message("⚠️ 正在处理中，请稍候", "warning")
            return

        # 禁用按钮
        self.set_buttons_enabled(False)

        # 创建并启动工作线程
        self.current_worker = TextProcessWorker(text)
        self.current_worker.progress_updated.connect(self.update_progress)
        self.current_worker.status_updated.connect(self.update_status)
        self.current_worker.finished_signal.connect(self.on_text_process_finished)
        self.current_worker.error_signal.connect(self.on_process_error)

        # 显示进度对话框
        self.processing_dialog = ProcessingDialog("处理链接", "正在解析链接...", self)
        self.processing_dialog.show()

        self.current_worker.start()

    def process_folder(self):
        """处理XML目录"""
        if self.current_worker and self.current_worker.isRunning():
            self.show_message("⚠️ 正在处理中，请稍候", "warning")
            return

        # 禁用按钮
        self.set_buttons_enabled(False)

        # 创建并启动工作线程
        self.current_worker = XMLProcessWorker()
        self.current_worker.progress_updated.connect(self.update_progress)
        self.current_worker.status_updated.connect(self.update_status)
        self.current_worker.finished_signal.connect(self.on_xml_process_finished)
        self.current_worker.error_signal.connect(self.on_process_error)

        # 显示进度对话框
        self.processing_dialog = ProcessingDialog("处理XML文件", "正在扫描XML文件...", self)
        self.processing_dialog.show()

        self.current_worker.start()

    def clear_xml_files(self):
        """清除XML文件"""
        folder = os.path.dirname(os.path.abspath(__file__))
        xml_files = list(Path(folder).glob('**/*.xml'))

        if not xml_files:
            self.show_message("⚠️ 当前目录下没有XML文件", "info")
            return

        # 确认对话框
        reply = QMessageBox.question(
            self,
            "确认删除",
            f"⚠️ 确定要删除当前目录下的 {len(xml_files)} 个XML文件吗？\n\n此操作不可撤销！",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            deleted_count = 0
            for xml_file in xml_files:
                try:
                    os.remove(xml_file)
                    deleted_count += 1
                except Exception as e:
                    print(f"删除文件 {xml_file} 失败: {e}")

            # 清理数据
            self.xml_file_links = {}
            self.xml_result.clear()

            if deleted_count == len(xml_files):
                self.show_message(f"✅ 已成功删除 {deleted_count} 个XML文件", "success")
            else:
                self.show_message(f"⚠️ 删除了 {deleted_count}/{len(xml_files)} 个XML文件", "warning")
        else:
            self.show_message("🚫 已取消删除操作", "info")

    def copy_video_links(self):
        """复制视频链接"""
        links = self.video_links.copy()
        if not links and self.xml_file_links:
            links = []
            for v_links, _ in self.xml_file_links.values():
                links.extend(v_links)

        if links:
            clipboard = QApplication.clipboard()
            clipboard.setText("\n".join(links))
            self.show_message(f"📋 已复制 {len(links)} 条视频链接", "success")
        else:
            self.show_message("⚠️ 没有可复制的视频链接", "warning")

    def copy_all_links(self):
        """复制全部链接"""
        links = self.video_links + self.other_links
        if not links and self.xml_file_links:
            links = []
            for v_links, o_links in self.xml_file_links.values():
                links.extend(v_links)
                links.extend(o_links)

        if links:
            clipboard = QApplication.clipboard()
            clipboard.setText("\n".join(links))
            video_count = len(self.video_links) if self.video_links else sum(len(v) for v, _ in self.xml_file_links.values())
            other_count = len(self.other_links) if self.other_links else sum(len(o) for _, o in self.xml_file_links.values())
            self.show_message(f"📋 已复制全部链接：视频 {video_count} 条，其他 {other_count} 条", "success")
        else:
            self.show_message("⚠️ 没有可复制的链接", "warning")

    def update_progress(self, current: int, total: int):
        """更新进度"""
        if self.processing_dialog:
            self.processing_dialog.set_progress(current, total)

    def update_status(self, status: str):
        """更新状态"""
        if self.processing_dialog:
            self.processing_dialog.update_status(status)

    def on_text_process_finished(self, result):
        """文本处理完成"""
        video_links, other_links = result
        self.video_links = video_links
        self.other_links = other_links

        # 显示结果
        self.display_text_results()

        # 更新统计
        total_count = len(video_links) + len(other_links)
        if total_count > 0:
            self.stats_label.setText(f"📊 视频: {len(video_links)} | 其他: {len(other_links)} | 总计: {total_count}")
            self.show_message(f"✅ 转换成功！视频 {len(video_links)} 条，其他 {len(other_links)} 条", "success")
        else:
            self.stats_label.setText("📊 未找到有效链接")
            self.show_message("⚠️ 未找到有效的ED2K链接", "warning")

        # 切换到文本结果标签页
        self.result_tabs.setCurrentIndex(0)

        self.cleanup_processing()

    def on_xml_process_finished(self, result):
        """XML处理完成"""
        self.xml_file_links = result

        # 显示结果
        self.display_xml_results()

        # 更新统计
        total_v = sum(len(v) for v, _ in self.xml_file_links.values())
        total_o = sum(len(o) for _, o in self.xml_file_links.values())

        if total_v + total_o > 0:
            self.stats_label.setText(f"📊 XML文件: {len(self.xml_file_links)} | 视频: {total_v} | 其他: {total_o}")
            self.show_message(f"✅ XML处理完成！视频 {total_v} 条，其他 {total_o} 条", "success")
        else:
            self.stats_label.setText("📊 XML文件中未找到有效链接")
            self.show_message("⚠️ XML文件中未找到有效链接", "warning")

        # 切换到XML结果标签页
        self.result_tabs.setCurrentIndex(1)

        self.cleanup_processing()

    def on_process_error(self, error_msg: str):
        """处理错误"""
        self.show_message(f"❌ {error_msg}", "error")
        self.cleanup_processing()

    def cleanup_processing(self):
        """清理处理状态"""
        if self.processing_dialog:
            self.processing_dialog.close()
            self.processing_dialog = None

        self.set_buttons_enabled(True)

        if self.current_worker:
            self.current_worker.quit()
            self.current_worker.wait()
            self.current_worker = None

    def display_text_results(self):
        """显示文本转换结果"""
        self.text_result.clear()

        if not self.video_links and not self.other_links:
            self.text_result.setPlainText("暂无转换结果\n\n请在输入框中粘贴 ED2K 链接，然后点击\"转换文本\"按钮。")
            return

        result_text = []

        if self.video_links:
            result_text.append("🎬 视频文件 ED2K 链接")
            result_text.append("=" * 50)
            for i, link in enumerate(self.video_links, 1):
                result_text.append(f"{i:2d}. {link}")
            result_text.append("")

        if self.other_links:
            result_text.append("📄 其他文件 ED2K 链接")
            result_text.append("=" * 50)
            for i, link in enumerate(self.other_links, 1):
                result_text.append(f"{i:2d}. {link}")

        self.text_result.setPlainText("\n".join(result_text))

    def display_xml_results(self):
        """显示XML处理结果"""
        self.xml_result.clear()

        if not self.xml_file_links:
            self.xml_result.setPlainText("暂无XML处理结果\n\n请点击\"处理XML目录\"按钮来扫描当前目录下的XML文件。")
            return

        result_text = []

        for xml_name, (v_links, o_links) in self.xml_file_links.items():
            result_text.append(f"📁 {xml_name}")
            result_text.append("─" * 60)

            if v_links:
                result_text.append("🎬 视频文件")
                for i, link in enumerate(v_links, 1):
                    result_text.append(f"  {i:2d}. {link}")
                result_text.append("")

            if o_links:
                result_text.append("📄 其他文件")
                for i, link in enumerate(o_links, 1):
                    result_text.append(f"  {i:2d}. {link}")
                result_text.append("")

            result_text.append("═" * 80)
            result_text.append("")

        self.xml_result.setPlainText("\n".join(result_text))

    def set_buttons_enabled(self, enabled: bool):
        """设置按钮启用状态"""
        buttons = [
            self.convert_btn, self.process_xml_btn, self.clear_xml_btn,
            self.copy_video_btn, self.copy_all_btn
        ]
        for button in buttons:
            button.setEnabled(enabled)

    def show_message(self, message: str, msg_type: str = "info"):
        """显示消息"""
        # 更新状态栏
        self.status_label.setText(message)

        # 根据类型显示不同的消息框
        if msg_type == "error":
            QMessageBox.critical(self, "错误", message)
        elif msg_type == "warning":
            QMessageBox.warning(self, "警告", message)
        elif msg_type == "success":
            # 成功消息只在状态栏显示，不弹窗
            pass
        else:  # info
            # 信息消息只在状态栏显示，不弹窗
            pass

def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用信息
    app.setApplicationName(APP_NAME)
    app.setApplicationVersion(APP_VERSION)
    app.setOrganizationName("ED2K Tools")

    # 设置应用样式
    app.setStyle("Fusion")  # 使用Fusion样式作为基础

    # 设置全局字体
    font = QFont(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_BODY)
    app.setFont(font)

    # 创建并显示主窗口
    window = ED2KMainWindow()
    window.show()

    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
