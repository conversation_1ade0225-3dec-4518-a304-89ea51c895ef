#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试启动脚本 - 用于检查程序是否能正常启动
"""

import sys
import traceback

def test_imports():
    """测试导入"""
    print("🔍 测试导入模块...")
    
    try:
        import configparser
        print("✅ configparser 导入成功")
    except Exception as e:
        print(f"❌ configparser 导入失败: {e}")
        return False
    
    try:
        from PyQt6.QtWidgets import QApplication
        print("✅ PyQt6.QtWidgets 导入成功")
    except Exception as e:
        print(f"❌ PyQt6.QtWidgets 导入失败: {e}")
        return False
    
    try:
        from PyQt6.QtCore import Qt
        print("✅ PyQt6.QtCore 导入成功")
    except Exception as e:
        print(f"❌ PyQt6.QtCore 导入失败: {e}")
        return False
    
    try:
        from PyQt6.QtGui import QFont
        print("✅ PyQt6.QtGui 导入成功")
    except Exception as e:
        print(f"❌ PyQt6.QtGui 导入失败: {e}")
        return False
    
    return True

def test_main_import():
    """测试主程序导入"""
    print("\n🔍 测试主程序导入...")
    
    try:
        # 尝试导入主程序的各个类
        from ed2k_enhanced_gui import ConfigManager
        print("✅ ConfigManager 导入成功")
        
        from ed2k_enhanced_gui import BitCometController
        print("✅ BitCometController 导入成功")
        
        from ed2k_enhanced_gui import EnhancedED2KMainWindow
        print("✅ EnhancedED2KMainWindow 导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 主程序导入失败: {e}")
        traceback.print_exc()
        return False

def test_class_creation():
    """测试类创建"""
    print("\n🔍 测试类创建...")
    
    try:
        from ed2k_enhanced_gui import ConfigManager
        config = ConfigManager()
        print("✅ ConfigManager 创建成功")
        
        from ed2k_enhanced_gui import BitCometController
        bitcomet = BitCometController(config)
        print("✅ BitCometController 创建成功")
        
        return True
    except Exception as e:
        print(f"❌ 类创建失败: {e}")
        traceback.print_exc()
        return False

def test_app_creation():
    """测试应用程序创建"""
    print("\n🔍 测试应用程序创建...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from ed2k_enhanced_gui import EnhancedED2KMainWindow
        
        app = QApplication(sys.argv)
        print("✅ QApplication 创建成功")
        
        window = EnhancedED2KMainWindow()
        print("✅ EnhancedED2KMainWindow 创建成功")
        
        # 不显示窗口，只测试创建
        app.quit()
        return True
    except Exception as e:
        print(f"❌ 应用程序创建失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 ED2K增强版启动测试")
    print("=" * 50)
    
    # 测试步骤
    tests = [
        ("基础模块导入", test_imports),
        ("主程序导入", test_main_import),
        ("类创建测试", test_class_creation),
        ("应用程序创建", test_app_creation),
    ]
    
    all_passed = True
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            result = test_func()
            if result:
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
                all_passed = False
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            traceback.print_exc()
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有测试通过！程序应该可以正常启动")
        print("💡 如果仍然闪退，请检查是否缺少依赖或权限问题")
    else:
        print("⚠️ 发现问题！请根据上述错误信息修复")
    
    print("\n📝 如果测试通过但仍然闪退，可能的原因：")
    print("1. 缺少PyQt6依赖：pip install PyQt6")
    print("2. 权限问题：以管理员身份运行")
    print("3. 系统兼容性问题：检查Python版本")
    print("4. 文件损坏：重新下载程序文件")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
