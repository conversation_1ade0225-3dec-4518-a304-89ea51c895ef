# ED2K 批量处理工具 - 使用说明

## 🎯 应用概述

ED2K 批量处理工具是一个现代化的桌面应用程序，专门用于批量处理和管理 ED2K 链接。采用 PyQt6 框架开发，具有简洁优雅的界面设计和强大的功能特性。

## ✨ 主要特性

### 🎨 界面设计
- **简约优雅**：采用现代化设计语言，界面简洁清爽
- **合理布局**：左右分栏设计，操作区域和结果显示分离
- **柔和配色**：使用中性色调，减少视觉疲劳
- **响应式设计**：支持窗口缩放，适配不同屏幕

### 🚀 核心功能
- **文本链接转换**：批量处理粘贴的 ED2K 链接
- **XML 文件处理**：扫描并处理目录下的 XML 文件
- **智能分类**：自动区分视频文件和其他文件类型
- **批量复制**：支持选择性复制视频链接或全部链接
- **文件管理**：一键清理 XML 文件

### ⚡ 性能优化
- **多线程处理**：大文件处理不阻塞界面
- **实时进度**：显示处理进度和状态信息
- **内存优化**：高效的数据处理和存储

## 🖥️ 界面布局

### 左侧操作区域
1. **输入链接**：粘贴 ED2K 链接的文本框
2. **操作按钮**：
   - 🔄 转换文本
   - 📁 处理XML目录
   - 🎬 复制视频链接
   - 📋 复制全部链接
   - 🗑️ 清除XML文件
3. **统计信息**：显示处理结果统计

### 右侧结果区域
- **文本转换**：显示从输入文本转换的结果
- **XML处理**：显示从XML文件提取的结果

## 📖 使用方法

### 1. 文本链接转换
1. 在左侧"输入链接"文本框中粘贴 ED2K 链接
2. 每行一个链接
3. 点击"🔄 转换文本"按钮
4. 在右侧"文本转换"标签页查看结果

### 2. XML 文件处理
1. 将 XML 文件放置在应用程序同目录下
2. 点击"📁 处理XML目录"按钮
3. 应用会自动扫描并处理所有 XML 文件
4. 在右侧"XML处理"标签页查看结果

### 3. 复制链接
- **复制视频链接**：只复制视频文件的 ED2K 链接
- **复制全部链接**：复制所有文件的 ED2K 链接

### 4. 文件管理
- **清除XML文件**：删除当前目录下的所有 XML 文件（谨慎使用）

## ⌨️ 快捷键

| 快捷键 | 功能 |
|--------|------|
| `Ctrl+Enter` | 转换文本 |
| `Ctrl+O` | 处理XML目录 |
| `Ctrl+Shift+V` | 复制视频链接 |
| `Ctrl+Shift+A` | 复制全部链接 |

## 🔧 技术特性

### 支持的文件格式
- **视频格式**：.mp4, .mkv, .avi, .mov, .wmv, .flv, .webm, .m4v, .3gp, .ts, .mts
- **音频格式**：.mp3, .flac, .wav, .aac, .ogg, .m4a, .wma
- **压缩格式**：.zip, .rar, .7z, .tar, .gz, .bz2

### 广告过滤
自动识别并过滤包含以下内容的链接：
- 【更多...】
- 更多无水印...
- www.网址
- BBQDDQ
- BPHDTV

## 🛠️ 安装要求

### 系统要求
- Windows 7/8/10/11
- Python 3.7 或更高版本

### 依赖库
```bash
pip install PyQt6
```

## 🚀 启动方式

### 方式一：批处理文件
双击 `启动ED2K工具.bat` 文件

### 方式二：命令行
```bash
python ed2k_gui_pyqt6.py
```

## 📝 注意事项

1. **数据安全**：清除XML文件操作不可撤销，请谨慎使用
2. **文件路径**：XML文件需要放在应用程序同目录下
3. **链接格式**：确保输入的是有效的 ED2K 链接格式
4. **性能考虑**：处理大量文件时请耐心等待

## 🔄 版本历史

### v5.0.0 (当前版本)
- 全新 PyQt6 界面框架
- 简约优雅的设计风格
- 左右分栏布局优化
- 柔和的配色方案
- 多线程处理优化
- 完善的错误处理

### v4.0.0
- 现代化 Tkinter 界面
- 液态玻璃效果设计
- 动画和交互优化

### v3.1.0
- 基础功能实现
- 传统界面设计

## 📞 技术支持

如果您在使用过程中遇到问题，请检查：
1. Python 版本是否符合要求
2. PyQt6 是否正确安装
3. 文件权限是否充足
4. 输入格式是否正确

---

**© 2024 ED2K 批量处理工具 - 让文件管理更简单**
