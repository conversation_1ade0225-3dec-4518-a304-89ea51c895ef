#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BitComet启动测试工具
用于测试不同的BitComet启动方式
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def test_bitcomet_path(bitcomet_path):
    """测试BitComet路径是否有效"""
    print(f"🔍 测试BitComet路径: {bitcomet_path}")
    
    if not bitcomet_path:
        print("❌ 路径为空")
        return False
    
    if not os.path.exists(bitcomet_path):
        print("❌ 文件不存在")
        return False
    
    if not bitcomet_path.lower().endswith('.exe'):
        print("❌ 不是可执行文件")
        return False
    
    print("✅ 路径检查通过")
    return True

def test_method_1(bitcomet_path, magnet_url):
    """方法1: 直接启动BitComet并传递磁力链接"""
    print("\n🧪 测试方法1: subprocess.run")
    
    try:
        result = subprocess.run(
            [bitcomet_path, magnet_url], 
            capture_output=True, 
            text=True, 
            timeout=10,
            shell=False
        )
        
        print(f"返回码: {result.returncode}")
        if result.stdout:
            print(f"标准输出: {result.stdout}")
        if result.stderr:
            print(f"错误输出: {result.stderr}")
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("⏰ 超时，但BitComet可能已启动")
        return True
    except Exception as e:
        print(f"❌ 异常: {e}")
        return False

def test_method_2(bitcomet_path, magnet_url):
    """方法2: 使用shell=True"""
    print("\n🧪 测试方法2: shell=True")
    
    try:
        cmd = f'"{bitcomet_path}" "{magnet_url}"'
        print(f"执行命令: {cmd}")
        
        process = subprocess.Popen(cmd, shell=True)
        time.sleep(3)  # 等待启动
        
        print("✅ 命令执行完成")
        return True
        
    except Exception as e:
        print(f"❌ 异常: {e}")
        return False

def test_method_3(bitcomet_path):
    """方法3: 只启动BitComet，不传递参数"""
    print("\n🧪 测试方法3: 只启动BitComet")
    
    try:
        process = subprocess.Popen([bitcomet_path], shell=False)
        time.sleep(3)  # 等待启动
        
        print("✅ BitComet启动完成")
        return True
        
    except Exception as e:
        print(f"❌ 异常: {e}")
        return False

def test_method_4(bitcomet_path, magnet_url):
    """方法4: 使用os.system"""
    print("\n🧪 测试方法4: os.system")
    
    try:
        cmd = f'"{bitcomet_path}" "{magnet_url}"'
        print(f"执行命令: {cmd}")
        
        result = os.system(cmd)
        print(f"返回码: {result}")
        
        return result == 0
        
    except Exception as e:
        print(f"❌ 异常: {e}")
        return False

def find_bitcomet_automatically():
    """自动查找BitComet"""
    print("🔍 自动查找BitComet...")
    
    possible_paths = [
        r"C:\Program Files\BitComet\BitComet.exe",
        r"C:\Program Files (x86)\BitComet\BitComet.exe",
        r"D:\Program Files\BitComet\BitComet.exe",
        r"D:\Program Files (x86)\BitComet\BitComet.exe",
    ]
    
    # 检查常见路径
    for path in possible_paths:
        if os.path.exists(path):
            print(f"✅ 找到BitComet: {path}")
            return path
    
    # 搜索当前目录及子目录
    current_dir = Path(".")
    for exe_file in current_dir.rglob("BitComet*.exe"):
        print(f"✅ 找到BitComet: {exe_file}")
        return str(exe_file)
    
    print("❌ 未找到BitComet")
    return None

def main():
    """主函数"""
    print("🚀 BitComet启动测试工具")
    print("=" * 60)
    
    # 测试磁力链接
    test_magnet = "magnet:?xt=urn:btih:f2b9671662f7c3ed61c33a8f1be828098017430c&dn=test"
    
    # 获取BitComet路径
    bitcomet_path = input("请输入BitComet路径 (留空自动查找): ").strip()
    
    if not bitcomet_path:
        bitcomet_path = find_bitcomet_automatically()
        if not bitcomet_path:
            print("❌ 无法找到BitComet，请手动指定路径")
            return
    
    # 测试路径
    if not test_bitcomet_path(bitcomet_path):
        print("❌ BitComet路径无效")
        return
    
    print(f"\n📋 测试信息:")
    print(f"BitComet路径: {bitcomet_path}")
    print(f"测试磁力链接: {test_magnet}")
    print("-" * 60)
    
    # 执行各种测试方法
    methods = [
        ("方法1: subprocess.run", lambda: test_method_1(bitcomet_path, test_magnet)),
        ("方法2: shell=True", lambda: test_method_2(bitcomet_path, test_magnet)),
        ("方法3: 只启动BitComet", lambda: test_method_3(bitcomet_path)),
        ("方法4: os.system", lambda: test_method_4(bitcomet_path, test_magnet)),
    ]
    
    results = []
    
    for method_name, test_func in methods:
        print(f"\n{'='*20} {method_name} {'='*20}")
        
        try:
            success = test_func()
            results.append((method_name, success))
            
            if success:
                print(f"✅ {method_name} 成功")
            else:
                print(f"❌ {method_name} 失败")
                
        except Exception as e:
            print(f"❌ {method_name} 异常: {e}")
            results.append((method_name, False))
        
        # 等待用户确认
        input("\n按回车键继续下一个测试...")
    
    # 显示测试结果总结
    print("\n" + "="*60)
    print("📊 测试结果总结:")
    print("-" * 60)
    
    for method_name, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{method_name}: {status}")
    
    # 推荐最佳方法
    successful_methods = [name for name, success in results if success]
    
    if successful_methods:
        print(f"\n💡 推荐使用: {successful_methods[0]}")
        print("\n🔧 修复建议:")
        print("1. 在程序中优先使用成功的方法")
        print("2. 添加多种方法的fallback机制")
        print("3. 提供更详细的错误信息给用户")
    else:
        print("\n❌ 所有方法都失败了")
        print("\n🔧 可能的原因:")
        print("1. BitComet路径不正确")
        print("2. BitComet版本不兼容")
        print("3. 系统权限问题")
        print("4. 杀毒软件阻止")
    
    print("\n按回车键退出...")
    input()

if __name__ == "__main__":
    main()
