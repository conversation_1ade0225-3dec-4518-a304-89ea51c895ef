# 🧲 BitComet集成使用说明

## 🚨 重要修复说明

针对用户反馈的磁力链接推送失败问题，我们进行了全面的修复和改进：

### ❌ 原问题
```
方法1失败: [WinError -2147221003] 找不到应用程序: 'magnet:?xt=urn:btih:...'
```

### ✅ 修复内容
1. **改进了BitComet启动逻辑** - 使用多种方法确保成功启动
2. **修复了路径保存功能** - 用户设置的路径现在会自动保存
3. **增强了错误处理** - 提供更详细的错误信息和解决建议
4. **优化了用户体验** - 添加处理状态显示和友好提示

## 🔧 BitComet路径设置

### 自动检测
程序会自动尝试在以下位置查找BitComet：
- `C:\Program Files\BitComet\BitComet.exe`
- `C:\Program Files (x86)\BitComet\BitComet.exe`
- `D:\Program Files\BitComet\BitComet.exe`
- `D:\Program Files (x86)\BitComet\BitComet.exe`

### 手动设置
如果自动检测失败，请手动设置：

1. **切换到"🧲 磁力链接"标签页**
2. **在"程序路径"输入框中输入完整路径**
   ```
   例如：D:\迅雷下载\比特彗星(BitComet)-v2.06 隐藏功能解锁版\比特彗星(BitComet)-v2.06\BitComet_x64.exe
   ```
3. **点击"🔍 测试"按钮验证路径**
4. **路径验证成功后会自动保存到配置文件**

### 路径保存机制
- ✅ 手动设置的路径会自动保存到 `ed2k_config.ini`
- ✅ 下次启动程序会自动加载保存的路径
- ✅ 测试成功的路径会立即保存

## 🧲 磁力链接处理

### 单个磁力链接
1. **粘贴磁力链接到输入框**
2. **点击"📥 添加到BitComet"**
3. **程序会尝试多种方法启动BitComet：**
   - 方法1: 直接启动BitComet并传递磁力链接
   - 方法2: 使用shell方式启动
   - 方法3: 启动BitComet并复制磁力链接到剪贴板

### 处理结果
- ✅ **成功**: BitComet启动并添加下载任务
- ⚠️ **部分成功**: BitComet启动，磁力链接复制到剪贴板，需手动粘贴
- ❌ **失败**: 显示详细错误信息和解决建议

## 🔄 批量处理功能

### 支持的输入格式
```
# 磁力链接
magnet:?xt=urn:btih:f2b9671662f7c3ed61c33a8f1be828098017430c&dn=电影名

# 种子文件路径
C:\Downloads\电影.torrent
D:\种子文件\电视剧.torrent

# 混合输入（每行一个）
magnet:?xt=urn:btih:...
C:\Downloads\movie1.torrent
magnet:?xt=urn:btih:...
D:\torrents\movie2.torrent
```

### 批量处理流程
1. **切换到"🔄 批量处理"标签页**
2. **粘贴种子文件路径或磁力链接（每行一个）**
3. **点击"📥 添加到BitComet"**
4. **等待处理完成，查看结果**
5. **点击"🔍 检查ED2K生成状态"监控进度**

### 处理逻辑
- **磁力链接**: 直接传递给BitComet启动
- **种子文件**: 
  - 优先使用BitComet直接打开
  - 备用方案：使用系统默认程序打开
- **无效项目**: 自动跳过并提示

## 🔍 ED2K生成监控

### 自动检查
程序会自动扫描当前目录的XML文件，分析最新生成的ED2K链接：

1. **找到最新XML文件**
2. **解析视频文件和其他文件**
3. **显示文件数量和部分文件名**
4. **提供切换到单文件处理的建议**

### 手动检查
- 切换到"📁 单文件处理"标签页
- 查看完整的XML文件列表
- 选择文件查看详细的ED2K链接
- 复制需要的链接

## 🛠️ 故障排除

### 磁力链接添加失败
**可能原因：**
1. BitComet路径不正确
2. BitComet版本不兼容
3. 系统权限问题
4. 杀毒软件阻止

**解决方案：**
1. **重新设置BitComet路径**
   - 确保路径指向正确的exe文件
   - 点击"🔍 测试"验证路径

2. **手动启动BitComet测试**
   - 双击BitComet程序
   - 确保能正常启动

3. **以管理员身份运行**
   - 右键点击bat文件
   - 选择"以管理员身份运行"

4. **检查杀毒软件**
   - 将程序添加到白名单
   - 临时关闭实时保护测试

### 种子文件处理失败
**可能原因：**
1. 文件路径不存在
2. 文件格式不正确
3. 权限问题

**解决方案：**
1. **检查文件路径**
   - 确保路径完整且正确
   - 使用绝对路径

2. **验证种子文件**
   - 确保是有效的.torrent文件
   - 尝试手动双击打开

### BitComet无响应
**可能原因：**
1. BitComet已经在运行
2. 程序冲突
3. 系统资源不足

**解决方案：**
1. **检查任务管理器**
   - 结束现有的BitComet进程
   - 重新启动程序

2. **重启BitComet**
   - 完全退出BitComet
   - 重新启动程序

## 🧪 测试工具

我们提供了专门的测试工具 `bitcomet_test.py`：

### 使用方法
```bash
python bitcomet_test.py
```

### 测试内容
1. **路径验证** - 检查BitComet路径是否有效
2. **启动方法测试** - 测试4种不同的启动方法
3. **兼容性检查** - 验证与当前系统的兼容性
4. **推荐方案** - 提供最佳的启动方法建议

### 测试结果
- ✅ **成功的方法** - 程序会优先使用
- ❌ **失败的方法** - 程序会跳过
- 💡 **推荐方案** - 根据测试结果提供建议

## 📋 配置文件说明

### ed2k_config.ini
程序会自动创建和维护配置文件：

```ini
[BitComet]
path = D:\BitComet\BitComet.exe
auto_start = true

[UI]
splitter_sizes = 300,700
window_geometry = 
```

### 配置项说明
- **path**: BitComet程序路径
- **auto_start**: 是否自动启动BitComet
- **splitter_sizes**: 界面分割器大小
- **window_geometry**: 窗口位置和大小

## 💡 使用技巧

### 1. 批量处理最佳实践
- 一次不要添加太多项目（建议10个以内）
- 等待前一批处理完成再添加新的
- 定期检查BitComet的下载状态

### 2. 路径设置技巧
- 使用完整的绝对路径
- 避免路径中包含特殊字符
- 定期验证路径的有效性

### 3. 错误处理
- 仔细阅读错误提示信息
- 使用测试工具诊断问题
- 查看控制台输出获取详细信息

### 4. 性能优化
- 关闭不必要的杀毒软件实时保护
- 确保系统有足够的内存
- 定期清理临时文件

## 🔄 版本更新

### v6.0.0 修复版
- ✅ 修复磁力链接推送失败问题
- ✅ 添加路径自动保存功能
- ✅ 改进错误处理和用户提示
- ✅ 增强批量处理稳定性
- ✅ 提供专门的测试工具

### 下一版本计划
- 🔄 支持更多下载工具
- 🔄 添加下载进度监控
- 🔄 改进界面响应速度
- 🔄 增加更多自定义选项

## 📞 技术支持

如果遇到问题，请提供以下信息：
1. **操作系统版本**
2. **BitComet版本和路径**
3. **错误信息截图**
4. **测试工具的运行结果**
5. **具体的操作步骤**

这样我们能更快地帮您解决问题！
