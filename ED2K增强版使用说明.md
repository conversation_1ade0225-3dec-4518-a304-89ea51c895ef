# 🚀 ED2K批量处理工具 - 增强版使用说明

## 📋 新增功能概览

### ✨ 主要新功能
1. **🧲 磁力链接直接推送到BitComet** - 无需手动复制粘贴
2. **📁 单个XML文件处理** - 精确处理指定文件
3. **🔄 实时文件监控** - 自动检测新生成的XML文件
4. **📊 智能文件分类** - 自动区分视频和其他文件
5. **💫 现代化界面** - 更美观的用户体验

---

## 🎯 功能详细说明

### 1. 🧲 磁力链接处理

**功能描述：**
- 直接将磁力链接推送到BitComet
- 自动检测BitComet安装路径
- 一键添加，无需手动操作

**使用步骤：**
1. 复制磁力链接
2. 粘贴到"磁力链接"输入框
3. 点击"📥 添加到BitComet"按钮
4. BitComet会自动启动并添加任务

**支持的BitComet路径：**
- `C:\Program Files\BitComet\BitComet.exe`
- `C:\Program Files (x86)\BitComet\BitComet.exe`
- `D:\Program Files\BitComet\BitComet.exe`
- 自动从注册表检测

### 2. 📁 单个XML文件处理

**功能描述：**
- 实时显示当前目录下的所有XML文件
- 点击选择特定XML文件进行处理
- 分别显示视频文件和其他文件的ED2K链接
- 支持单独复制不同类型的链接

**使用步骤：**
1. 切换到"📁 单文件处理"标签页
2. 在左侧文件列表中选择XML文件
3. 等待处理完成
4. 选择复制需要的链接类型：
   - 📹 复制视频链接
   - 📄 复制其他链接
   - 📋 复制全部链接

**文件列表功能：**
- 显示文件名、大小、修改时间
- 实时状态更新
- 🔄 手动刷新按钮

### 3. 🔄 实时文件监控

**功能描述：**
- 自动监控当前目录下的XML文件变化
- 检测新文件添加、文件修改、文件删除
- 实时更新文件列表状态

**监控事件：**
- ✅ **新文件添加** - 自动添加到列表
- 🔄 **文件修改** - 标记为"已修改"状态
- ❌ **文件删除** - 自动从列表移除

**状态指示：**
- **就绪** - 文件正常，可以处理
- **已修改** - 文件已被修改，建议重新处理
- **处理中** - 正在解析文件内容

### 4. 📦 批量处理（保留功能）

**功能描述：**
- 批量处理多个ED2K链接
- 自动分类视频和其他文件
- 显示处理统计信息

**使用方法：**
1. 在输入框中粘贴ED2K链接（每行一个）
2. 点击"🔄 处理链接"
3. 查看分类结果

---

## 🎨 界面布局说明

### 主窗口结构
```
┌─────────────────────────────────────────────────────────────┐
│  🎯 ED2K 批量处理工具 Enhanced v6.0.0                       │
│  ✨ 新功能: 单文件处理 | 实时监控 | BitComet集成              │
├─────────────────────────────────────────────────────────────┤
│  🧲 磁力链接  │  📁 单文件处理  │  📦 批量处理              │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  [当前标签页内容]                                            │
│                                                             │
├─────────────────────────────────────────────────────────────┤
│  状态栏: 就绪                                               │
└─────────────────────────────────────────────────────────────┘
```

### 标签页详情

#### 🧲 磁力链接标签页
```
┌─────────────────────────────────────────────────────────────┐
│  🧲 磁力链接处理                                             │
├─────────────────────────────────────────────────────────────┤
│  [磁力链接输入框]                    [📥 添加到BitComet]      │
├─────────────────────────────────────────────────────────────┤
│  ✅ BitComet已找到: C:\Program Files\BitComet\BitComet.exe  │
└─────────────────────────────────────────────────────────────┘
```

#### 📁 单文件处理标签页
```
┌─────────────────────────────────────────────────────────────┐
│  📁 XML文件列表        │  📁 选中文件处理结果                │
│  ┌─────────────────┐   │  ┌─────────────────────────────────┐ │
│  │ 文件名 │大小│时间│   │  │ 📹 复制视频 📄 复制其他 📋 全部 │ │
│  ├─────────────────┤   │  ├─────────────────────────────────┤ │
│  │ file1.xml      │   │  │                                 │ │
│  │ file2.xml      │   │  │  [处理结果显示区域]              │ │
│  │ file3.xml      │   │  │                                 │ │
│  └─────────────────┘   │  └─────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

---

## ⚙️ 配置和设置

### BitComet检测逻辑
1. **注册表检测** - 从Windows注册表读取安装路径
2. **常见路径扫描** - 检查常见安装目录
3. **状态显示** - 实时显示检测结果

### 文件监控设置
- **监控目录** - 当前脚本所在目录及子目录
- **文件类型** - 仅监控.xml文件
- **更新频率** - 实时监控，立即响应

### 界面主题
- **现代化设计** - 扁平化风格
- **响应式布局** - 自适应窗口大小
- **emoji图标** - 直观的功能标识

---

## 🔧 快捷键说明

| 快捷键 | 功能 |
|--------|------|
| `Ctrl+Q` | 退出程序 |
| `F5` | 刷新文件列表 |
| `Ctrl+C` | 复制选中内容 |
| `Ctrl+V` | 粘贴内容 |

---

## 🚀 使用场景示例

### 场景1：处理新下载的XML文件
1. 下载完成后，XML文件自动出现在文件列表
2. 点击选择该文件
3. 查看处理结果，复制需要的链接

### 场景2：快速添加磁力链接到BitComet
1. 从网站复制磁力链接
2. 切换到"磁力链接"标签页
3. 粘贴链接，点击添加
4. BitComet自动启动并开始下载

### 场景3：批量处理已有的ED2K链接
1. 切换到"批量处理"标签页
2. 粘贴多个ED2K链接
3. 点击处理，查看分类结果

---

## ❗ 注意事项

### BitComet集成
- 确保BitComet已正确安装
- 首次使用时可能需要确认关联
- 支持BitComet 1.x版本

### 文件监控
- 监控功能在程序运行期间持续工作
- 大量文件变化时可能有轻微延迟
- 建议定期使用F5手动刷新

### 性能优化
- 大文件处理时请耐心等待
- 建议关闭不必要的其他程序
- 定期清理临时文件

---

## 🐛 常见问题解决

### Q: BitComet无法自动检测？
**A:** 
1. 检查BitComet是否正确安装
2. 尝试手动指定BitComet路径
3. 以管理员权限运行程序

### Q: 文件监控不工作？
**A:**
1. 确保有文件读取权限
2. 检查文件路径是否正确
3. 尝试手动刷新（F5）

### Q: XML文件处理失败？
**A:**
1. 检查XML文件格式是否正确
2. 确保文件没有被其他程序占用
3. 查看错误信息提示

### Q: 界面显示异常？
**A:**
1. 检查系统字体设置
2. 尝试调整窗口大小
3. 重启程序

---

## 🔄 版本更新记录

### v6.0.0 Enhanced (当前版本)
- ✅ 新增磁力链接直接推送到BitComet
- ✅ 新增单个XML文件处理功能
- ✅ 新增实时文件监控
- ✅ 优化界面设计和用户体验
- ✅ 改进错误处理和状态提示

### v5.0.0 PyQt6
- ✅ 升级到PyQt6框架
- ✅ 现代化界面设计
- ✅ 改进性能和稳定性

---

## 📞 技术支持

如果您在使用过程中遇到问题：

1. **查看错误信息** - 注意状态栏和提示信息
2. **检查系统环境** - 确保Python和PyQt6正确安装
3. **重启程序** - 简单重启可以解决大部分问题
4. **查看日志** - 控制台输出可能包含有用信息

---

**🎉 享受使用增强版ED2K处理工具！**
