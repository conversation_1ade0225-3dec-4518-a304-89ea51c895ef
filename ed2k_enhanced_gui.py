#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ED2K 批量处理工具 - 增强版
新增功能：
1. 指定复制单个XML文件的ED2K链接
2. 实时监控新生成的XML文件
3. 支持复制磁力链接直接推送到BitComet

作者: AI Assistant
版本: 6.0.0 Enhanced
"""

import sys
import os
import re
import xml.etree.ElementTree as ET
import urllib.parse
from pathlib import Path
import threading
import json
import subprocess
import time
import shutil
import tempfile
import winreg
from typing import List, Tuple, Dict, Optional

from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QGridLayout, QTextEdit, QPlainTextEdit, QPushButton, QLabel,
    QFrame, QScrollArea, QProgressBar, QMessageBox, QDialog,
    QSplitter, QTabWidget, QGroupBox, QStatusBar, QMenuBar,
    QFileDialog, QCheckBox, QSpinBox, QComboBox, QListWidget,
    QListWidgetItem, QTreeWidget, QTreeWidgetItem, QHeaderView,
    QLineEdit, QTextBrowser
)
from PyQt6.QtCore import (
    Qt, QThread, pyqtSignal, QTimer, QPropertyAnimation,
    QEasingCurve, QRect, QSize, QPoint, QFileSystemWatcher
)
from PyQt6.QtGui import (
    QFont, QColor, QPalette, QPixmap, QPainter, QBrush,
    QLinearGradient, QIcon, QAction, QKeySequence, QTextCursor
)

APP_VERSION = "6.0.0"
APP_NAME = "ED2K 批量处理工具 Enhanced"
VIDEO_EXTS = {'.mp4', '.mkv', '.avi', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.3gp', '.ts', '.mts'}
AUDIO_EXTS = {'.mp3', '.flac', '.wav', '.aac', '.ogg', '.m4a', '.wma'}
ARCHIVE_EXTS = {'.zip', '.rar', '.7z', '.tar', '.gz', '.bz2'}
AD_PATTERN = re.compile(r"【更多.*?】|更多无水印.*?】|更多无水印.*?\.|www\.[^\s]+|BBQDDQ|BPHDTV", re.IGNORECASE)

class BitCometController:
    """BitComet控制器 - 用于自动添加磁力链接"""
    
    def __init__(self):
        self.bitcomet_path = self.find_bitcomet_path()
        self.temp_dir = tempfile.mkdtemp()
    
    def find_bitcomet_path(self) -> Optional[str]:
        """查找BitComet安装路径"""
        possible_paths = [
            r"C:\Program Files\BitComet\BitComet.exe",
            r"C:\Program Files (x86)\BitComet\BitComet.exe",
            r"D:\Program Files\BitComet\BitComet.exe",
            r"D:\Program Files (x86)\BitComet\BitComet.exe"
        ]
        
        # 从注册表查找
        try:
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                               r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\BitComet") as key:
                install_location = winreg.QueryValueEx(key, "InstallLocation")[0]
                bitcomet_exe = os.path.join(install_location, "BitComet.exe")
                if os.path.exists(bitcomet_exe):
                    return bitcomet_exe
        except:
            pass
        
        # 检查常见路径
        for path in possible_paths:
            if os.path.exists(path):
                return path
        
        return None
    
    def is_available(self) -> bool:
        """检查BitComet是否可用"""
        return self.bitcomet_path is not None and os.path.exists(self.bitcomet_path)
    
    def add_magnet_link(self, magnet_url: str) -> bool:
        """添加磁力链接到BitComet"""
        if not self.is_available():
            return False
        
        try:
            # 方法1: 直接启动BitComet并传递磁力链接
            subprocess.Popen([self.bitcomet_path, magnet_url])
            return True
        except Exception as e:
            print(f"添加磁力链接失败: {e}")
            return False
    
    def add_magnet_via_torrent_file(self, magnet_url: str) -> bool:
        """通过创建临时种子文件添加磁力链接"""
        try:
            # 创建临时种子文件
            temp_torrent = os.path.join(self.temp_dir, "temp.torrent")
            
            # 这里可以实现磁力链接转种子文件的逻辑
            # 或者使用其他方法
            
            if os.path.exists(temp_torrent):
                subprocess.Popen([self.bitcomet_path, temp_torrent])
                return True
            
        except Exception as e:
            print(f"通过种子文件添加失败: {e}")
        
        return False

class FileMonitor(QThread):
    """文件监控线程 - 实时监控XML文件变化"""
    
    file_added = pyqtSignal(str)      # 新文件添加信号
    file_modified = pyqtSignal(str)   # 文件修改信号
    file_removed = pyqtSignal(str)    # 文件删除信号
    
    def __init__(self, watch_directory: str, parent=None):
        super().__init__(parent)
        self.watch_directory = watch_directory
        self.file_watcher = QFileSystemWatcher()
        self.known_files = set()
        self.setup_watcher()
    
    def setup_watcher(self):
        """设置文件监控"""
        if os.path.exists(self.watch_directory):
            self.file_watcher.addPath(self.watch_directory)
            self.file_watcher.directoryChanged.connect(self.on_directory_changed)
            
            # 初始化已知文件列表
            self.scan_initial_files()
    
    def scan_initial_files(self):
        """扫描初始文件"""
        try:
            for xml_file in Path(self.watch_directory).glob('**/*.xml'):
                self.known_files.add(str(xml_file))
        except Exception as e:
            print(f"扫描初始文件失败: {e}")
    
    def on_directory_changed(self, path: str):
        """目录变化处理"""
        try:
            current_files = set()
            for xml_file in Path(path).glob('**/*.xml'):
                current_files.add(str(xml_file))
            
            # 检查新增文件
            new_files = current_files - self.known_files
            for new_file in new_files:
                self.file_added.emit(new_file)
            
            # 检查删除文件
            removed_files = self.known_files - current_files
            for removed_file in removed_files:
                self.file_removed.emit(removed_file)
            
            # 更新已知文件列表
            self.known_files = current_files
            
        except Exception as e:
            print(f"处理目录变化失败: {e}")
    
    def add_watch_path(self, path: str):
        """添加监控路径"""
        if os.path.exists(path) and path not in self.file_watcher.directories():
            self.file_watcher.addPath(path)

class SingleXMLProcessor(QThread):
    """单个XML文件处理器"""
    
    finished_signal = pyqtSignal(str, list, list)  # 文件名, 视频链接, 其他链接
    error_signal = pyqtSignal(str, str)            # 文件名, 错误信息
    
    def __init__(self, xml_file_path: str, parent=None):
        super().__init__(parent)
        self.xml_file_path = xml_file_path
    
    def run(self):
        """处理单个XML文件"""
        try:
            file_name = os.path.basename(self.xml_file_path)
            tree = ET.parse(self.xml_file_path)
            root = tree.getroot()
            file_list = root.find('FileList')
            
            video_links = []
            other_links = []
            
            if file_list is not None:
                for file_entry in file_list.findall('FileEntry'):
                    relative_path_name = file_entry.get('RelativePathName')
                    if not relative_path_name:
                        continue
                    
                    if self.is_ad_filename(relative_path_name):
                        continue
                    
                    ext = os.path.splitext(relative_path_name)[1].lower()
                    size = file_entry.get('Size')
                    e_mule_hash = file_entry.get('eMuleHash')
                    
                    if not all([relative_path_name, size, e_mule_hash]):
                        continue
                    
                    if relative_path_name.lower().startswith('ed2k:'):
                        continue
                    
                    cleaned_name = self.clean_filename(relative_path_name)
                    e_mule_hash = e_mule_hash.upper()
                    encoded_filename = urllib.parse.quote(cleaned_name)
                    ed2k_link = f'ed2k://|file|{encoded_filename}|{size}|{e_mule_hash}|/'
                    
                    if ext in VIDEO_EXTS:
                        video_links.append(ed2k_link)
                    else:
                        other_links.append(ed2k_link)
            
            self.finished_signal.emit(file_name, video_links, other_links)
            
        except Exception as e:
            self.error_signal.emit(os.path.basename(self.xml_file_path), str(e))
    
    def clean_filename(self, filename: str) -> str:
        """清理文件名"""
        return AD_PATTERN.sub("", filename).strip()
    
    def is_ad_filename(self, filename: str) -> bool:
        """检查文件名是否包含广告"""
        return bool(AD_PATTERN.search(filename))

class MagnetLinkWidget(QWidget):
    """磁力链接处理组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.bitcomet_controller = BitCometController()
        self.setup_ui()

    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)

        # 标题
        title_label = QLabel("🧲 磁力链接处理")
        title_label.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #2C3E50; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # BitComet路径设置区域
        path_group = QGroupBox("BitComet设置")
        path_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #BDC3C7;
                border-radius: 6px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        path_layout = QVBoxLayout(path_group)

        # 路径输入行
        path_input_layout = QHBoxLayout()

        path_label = QLabel("程序路径:")
        path_label.setMinimumWidth(60)
        path_input_layout.addWidget(path_label)

        self.bitcomet_path_input = QLineEdit()
        self.bitcomet_path_input.setPlaceholderText("BitComet.exe的完整路径...")
        self.bitcomet_path_input.setStyleSheet("""
            QLineEdit {
                padding: 6px 10px;
                border: 1px solid #BDC3C7;
                border-radius: 4px;
                font-size: 12px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3498DB;
            }
        """)
        path_input_layout.addWidget(self.bitcomet_path_input)

        # 浏览按钮
        browse_btn = QPushButton("📁 浏览")
        browse_btn.setStyleSheet("""
            QPushButton {
                background-color: #95A5A6;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 60px;
            }
            QPushButton:hover {
                background-color: #7F8C8D;
            }
        """)
        browse_btn.clicked.connect(self.browse_bitcomet_path)
        path_input_layout.addWidget(browse_btn)

        # 测试按钮
        test_btn = QPushButton("🔍 测试")
        test_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498DB;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 60px;
            }
            QPushButton:hover {
                background-color: #2980B9;
            }
        """)
        test_btn.clicked.connect(self.test_bitcomet_path)
        path_input_layout.addWidget(test_btn)

        path_layout.addLayout(path_input_layout)
        layout.addWidget(path_group)

        # 磁力链接输入
        input_layout = QHBoxLayout()

        self.magnet_input = QLineEdit()
        self.magnet_input.setPlaceholderText("粘贴磁力链接到这里...")
        self.magnet_input.setStyleSheet("""
            QLineEdit {
                padding: 8px 12px;
                border: 2px solid #BDC3C7;
                border-radius: 6px;
                font-size: 13px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3498DB;
            }
        """)
        input_layout.addWidget(self.magnet_input)

        # 添加到BitComet按钮
        self.add_to_bitcomet_btn = QPushButton("📥 添加到BitComet")
        self.add_to_bitcomet_btn.setStyleSheet("""
            QPushButton {
                background-color: #27AE60;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1E8449;
            }
            QPushButton:disabled {
                background-color: #BDC3C7;
                color: #7F8C8D;
            }
        """)
        self.add_to_bitcomet_btn.clicked.connect(self.add_to_bitcomet)
        input_layout.addWidget(self.add_to_bitcomet_btn)

        layout.addLayout(input_layout)

        # 状态显示
        self.status_label = QLabel("")
        self.status_label.setStyleSheet("color: #7F8C8D; font-size: 12px; margin-top: 5px;")
        layout.addWidget(self.status_label)

        # 检查BitComet状态
        self.check_bitcomet_status()
    
    def check_bitcomet_status(self):
        """检查BitComet状态"""
        if self.bitcomet_controller.is_available():
            self.status_label.setText(f"✅ BitComet已找到: {self.bitcomet_controller.bitcomet_path}")
            self.bitcomet_path_input.setText(self.bitcomet_controller.bitcomet_path)
            self.add_to_bitcomet_btn.setEnabled(True)
        else:
            self.status_label.setText("❌ 未找到BitComet，请手动设置路径或确保已安装BitComet")
            self.add_to_bitcomet_btn.setEnabled(False)

    def browse_bitcomet_path(self):
        """浏览选择BitComet路径"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择BitComet程序",
            "C:\\Program Files",
            "可执行文件 (*.exe);;所有文件 (*.*)"
        )

        if file_path:
            self.bitcomet_path_input.setText(file_path)
            self.test_bitcomet_path()

    def test_bitcomet_path(self):
        """测试BitComet路径"""
        path = self.bitcomet_path_input.text().strip()

        if not path:
            self.status_label.setText("❌ 请输入BitComet路径")
            self.add_to_bitcomet_btn.setEnabled(False)
            return

        if not os.path.exists(path):
            self.status_label.setText("❌ 路径不存在")
            self.add_to_bitcomet_btn.setEnabled(False)
            return

        if not path.lower().endswith('.exe'):
            self.status_label.setText("❌ 请选择可执行文件(.exe)")
            self.add_to_bitcomet_btn.setEnabled(False)
            return

        # 更新BitComet控制器路径
        self.bitcomet_controller.bitcomet_path = path
        self.status_label.setText(f"✅ BitComet路径已设置: {path}")
        self.add_to_bitcomet_btn.setEnabled(True)

    def add_to_bitcomet(self):
        """添加磁力链接到BitComet"""
        magnet_url = self.magnet_input.text().strip()

        if not magnet_url:
            QMessageBox.warning(self, "警告", "请输入磁力链接")
            return

        if not magnet_url.startswith('magnet:?'):
            QMessageBox.warning(self, "警告", "请输入有效的磁力链接")
            return

        # 如果用户手动设置了路径，使用手动路径
        manual_path = self.bitcomet_path_input.text().strip()
        if manual_path and os.path.exists(manual_path):
            self.bitcomet_controller.bitcomet_path = manual_path

        if self.bitcomet_controller.add_magnet_link(magnet_url):
            QMessageBox.information(self, "成功", "磁力链接已添加到BitComet")
            self.magnet_input.clear()
        else:
            QMessageBox.critical(self, "失败", "添加磁力链接失败，请检查BitComet路径是否正确")

class XMLFileListWidget(QWidget):
    """XML文件列表组件"""
    
    file_selected = pyqtSignal(str)  # 文件选择信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.xml_files = {}  # 存储XML文件信息
        self.setup_ui()
        self.setup_monitor()
    
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        
        # 标题和刷新按钮
        header_layout = QHBoxLayout()
        
        title_label = QLabel("📁 XML文件列表")
        title_label.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #2C3E50;")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        self.refresh_btn = QPushButton("🔄 刷新")
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498DB;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980B9;
            }
        """)
        self.refresh_btn.clicked.connect(self.refresh_file_list)
        header_layout.addWidget(self.refresh_btn)
        
        layout.addLayout(header_layout)
        
        # 文件列表
        self.file_tree = QTreeWidget()
        self.file_tree.setHeaderLabels(["文件名", "大小", "修改时间", "状态"])
        self.file_tree.setStyleSheet("""
            QTreeWidget {
                border: 1px solid #BDC3C7;
                border-radius: 6px;
                background-color: white;
                alternate-background-color: #F8F9FA;
            }
            QTreeWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ECF0F1;
            }
            QTreeWidget::item:selected {
                background-color: #3498DB;
                color: white;
            }
            QTreeWidget::item:hover {
                background-color: #EBF3FD;
            }
        """)
        self.file_tree.itemClicked.connect(self.on_file_selected)
        layout.addWidget(self.file_tree)
        
        # 状态标签
        self.status_label = QLabel("正在扫描XML文件...")
        self.status_label.setStyleSheet("color: #7F8C8D; font-size: 12px; margin-top: 5px;")
        layout.addWidget(self.status_label)
        
        # 初始加载
        self.refresh_file_list()
    
    def setup_monitor(self):
        """设置文件监控"""
        current_dir = os.path.dirname(os.path.abspath(__file__))
        self.file_monitor = FileMonitor(current_dir)
        self.file_monitor.file_added.connect(self.on_file_added)
        self.file_monitor.file_modified.connect(self.on_file_modified)
        self.file_monitor.file_removed.connect(self.on_file_removed)
        self.file_monitor.start()
    
    def refresh_file_list(self):
        """刷新文件列表"""
        self.file_tree.clear()
        self.xml_files.clear()
        
        try:
            current_dir = os.path.dirname(os.path.abspath(__file__))
            xml_files = list(Path(current_dir).glob('**/*.xml'))
            
            for xml_file in xml_files:
                self.add_file_to_tree(str(xml_file))
            
            self.status_label.setText(f"找到 {len(xml_files)} 个XML文件")
            
        except Exception as e:
            self.status_label.setText(f"扫描失败: {e}")
    
    def add_file_to_tree(self, file_path: str):
        """添加文件到树形列表"""
        try:
            file_info = os.stat(file_path)
            file_name = os.path.basename(file_path)
            file_size = self.format_file_size(file_info.st_size)
            mod_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(file_info.st_mtime))
            
            item = QTreeWidgetItem([file_name, file_size, mod_time, "就绪"])
            item.setData(0, Qt.ItemDataRole.UserRole, file_path)
            self.file_tree.addTopLevelItem(item)
            
            self.xml_files[file_path] = {
                'name': file_name,
                'size': file_info.st_size,
                'mtime': file_info.st_mtime,
                'item': item
            }
            
        except Exception as e:
            print(f"添加文件到列表失败: {e}")
    
    def format_file_size(self, size: int) -> str:
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"
    
    def on_file_selected(self, item: QTreeWidgetItem, column: int):
        """文件选择处理"""
        file_path = item.data(0, Qt.ItemDataRole.UserRole)
        if file_path:
            self.file_selected.emit(file_path)
    
    def on_file_added(self, file_path: str):
        """新文件添加处理"""
        if file_path.endswith('.xml'):
            self.add_file_to_tree(file_path)
            self.status_label.setText(f"检测到新文件: {os.path.basename(file_path)}")
    
    def on_file_modified(self, file_path: str):
        """文件修改处理"""
        if file_path in self.xml_files:
            item = self.xml_files[file_path]['item']
            item.setText(3, "已修改")
            item.setBackground(3, QColor("#F39C12"))
    
    def on_file_removed(self, file_path: str):
        """文件删除处理"""
        if file_path in self.xml_files:
            item = self.xml_files[file_path]['item']
            index = self.file_tree.indexOfTopLevelItem(item)
            if index >= 0:
                self.file_tree.takeTopLevelItem(index)
            del self.xml_files[file_path]

class SingleFileResultWidget(QWidget):
    """单个文件结果显示组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_file = ""
        self.video_links = []
        self.other_links = []
        self.setup_ui()

    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)

        # 文件信息
        self.file_info_label = QLabel("请选择XML文件")
        self.file_info_label.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        self.file_info_label.setStyleSheet("color: #2C3E50; margin-bottom: 10px;")
        layout.addWidget(self.file_info_label)

        # 操作按钮
        button_layout = QHBoxLayout()

        self.copy_video_btn = QPushButton("📹 复制视频链接")
        self.copy_video_btn.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #C0392B;
            }
            QPushButton:disabled {
                background-color: #BDC3C7;
                color: #7F8C8D;
            }
        """)
        self.copy_video_btn.clicked.connect(self.copy_video_links)
        self.copy_video_btn.setEnabled(False)
        button_layout.addWidget(self.copy_video_btn)

        self.copy_other_btn = QPushButton("📄 复制其他链接")
        self.copy_other_btn.setStyleSheet("""
            QPushButton {
                background-color: #9B59B6;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #8E44AD;
            }
            QPushButton:disabled {
                background-color: #BDC3C7;
                color: #7F8C8D;
            }
        """)
        self.copy_other_btn.clicked.connect(self.copy_other_links)
        self.copy_other_btn.setEnabled(False)
        button_layout.addWidget(self.copy_other_btn)

        self.copy_all_btn = QPushButton("📋 复制全部链接")
        self.copy_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #34495E;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2C3E50;
            }
            QPushButton:disabled {
                background-color: #BDC3C7;
                color: #7F8C8D;
            }
        """)
        self.copy_all_btn.clicked.connect(self.copy_all_links)
        self.copy_all_btn.setEnabled(False)
        button_layout.addWidget(self.copy_all_btn)

        layout.addLayout(button_layout)

        # 结果显示
        self.result_text = QTextBrowser()
        self.result_text.setStyleSheet("""
            QTextBrowser {
                border: 1px solid #BDC3C7;
                border-radius: 6px;
                background-color: white;
                padding: 10px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 14px;
                line-height: 1.4;
            }
        """)
        layout.addWidget(self.result_text)

        # 状态标签
        self.status_label = QLabel("")
        self.status_label.setStyleSheet("color: #7F8C8D; font-size: 12px; margin-top: 5px;")
        layout.addWidget(self.status_label)

    def load_file(self, file_path: str):
        """加载文件"""
        self.current_file = file_path
        self.file_info_label.setText(f"📁 {os.path.basename(file_path)}")
        self.status_label.setText("正在处理...")

        # 启动处理线程
        self.processor = SingleXMLProcessor(file_path)
        self.processor.finished_signal.connect(self.on_processing_finished)
        self.processor.error_signal.connect(self.on_processing_error)
        self.processor.start()

    def on_processing_finished(self, file_name: str, video_links: List[str], other_links: List[str]):
        """处理完成"""
        self.video_links = video_links
        self.other_links = other_links

        # 更新界面
        self.update_result_display()
        self.update_button_states()

        total_count = len(video_links) + len(other_links)
        self.status_label.setText(f"处理完成: {len(video_links)} 个视频, {len(other_links)} 个其他文件, 共 {total_count} 个链接")

    def on_processing_error(self, file_name: str, error_msg: str):
        """处理错误"""
        self.status_label.setText(f"处理失败: {error_msg}")
        self.result_text.setText(f"处理文件 {file_name} 时发生错误:\n{error_msg}")

    def update_result_display(self):
        """更新结果显示"""
        html_content = """
        <style>
            body { font-family: 'Microsoft YaHei', sans-serif; }
            h3 { color: #2C3E50; margin-top: 20px; margin-bottom: 10px; }
            .filename { color: #34495E; font-weight: bold; margin-bottom: 5px; }
            .ed2k-link {
                background-color: #F8F9FA;
                border: 1px solid #E9ECEF;
                border-radius: 4px;
                padding: 8px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 13px;
                word-break: break-all;
                margin-bottom: 15px;
                color: #495057;
            }
            .file-item { margin-bottom: 20px; }
            .count { color: #7F8C8D; font-size: 12px; }
        </style>
        """

        html_content += f"<h3>📹 视频文件 <span class='count'>({len(self.video_links)} 个)</span></h3>"

        if self.video_links:
            for i, link in enumerate(self.video_links, 1):
                filename = self.extract_filename_from_link(link)
                html_content += f"""
                <div class='file-item'>
                    <div class='filename'>{i}. {filename}</div>
                    <div class='ed2k-link'>{link}</div>
                </div>
                """
        else:
            html_content += "<p><i>没有找到视频文件</i></p>"

        html_content += f"<h3>📄 其他文件 <span class='count'>({len(self.other_links)} 个)</span></h3>"

        if self.other_links:
            for i, link in enumerate(self.other_links, 1):
                filename = self.extract_filename_from_link(link)
                html_content += f"""
                <div class='file-item'>
                    <div class='filename'>{i}. {filename}</div>
                    <div class='ed2k-link'>{link}</div>
                </div>
                """
        else:
            html_content += "<p><i>没有找到其他文件</i></p>"

        self.result_text.setHtml(html_content)

    def extract_filename_from_link(self, ed2k_link: str) -> str:
        """从ED2K链接提取文件名"""
        try:
            parts = ed2k_link.split('|')
            if len(parts) >= 3:
                return urllib.parse.unquote(parts[2])
        except:
            pass
        return "未知文件"

    def update_button_states(self):
        """更新按钮状态"""
        has_video = len(self.video_links) > 0
        has_other = len(self.other_links) > 0
        has_any = has_video or has_other

        self.copy_video_btn.setEnabled(has_video)
        self.copy_other_btn.setEnabled(has_other)
        self.copy_all_btn.setEnabled(has_any)

    def copy_video_links(self):
        """复制视频链接"""
        if self.video_links:
            text = '\n'.join(self.video_links)
            QApplication.clipboard().setText(text)
            QMessageBox.information(self, "成功", f"已复制 {len(self.video_links)} 个视频链接到剪贴板")

    def copy_other_links(self):
        """复制其他链接"""
        if self.other_links:
            text = '\n'.join(self.other_links)
            QApplication.clipboard().setText(text)
            QMessageBox.information(self, "成功", f"已复制 {len(self.other_links)} 个其他链接到剪贴板")

    def copy_all_links(self):
        """复制全部链接"""
        all_links = self.video_links + self.other_links
        if all_links:
            text = '\n'.join(all_links)
            QApplication.clipboard().setText(text)
            QMessageBox.information(self, "成功", f"已复制 {len(all_links)} 个链接到剪贴板")

class EnhancedED2KMainWindow(QMainWindow):
    """增强版主窗口"""

    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_shortcuts()
        self.setup_style()

    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle(f"{APP_NAME} v{APP_VERSION}")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # 创建标题
        self.create_header(main_layout)

        # 创建标签页
        self.create_tabs(main_layout)

        # 创建状态栏
        self.create_status_bar()

    def create_header(self, layout: QVBoxLayout):
        """创建标题区域"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498DB, stop:1 #2980B9);
                border-radius: 10px;
                padding: 20px;
            }
        """)

        header_layout = QHBoxLayout(header_frame)

        # 标题和版本
        title_layout = QVBoxLayout()

        title_label = QLabel(APP_NAME)
        title_label.setFont(QFont("Microsoft YaHei", 20, QFont.Weight.Bold))
        title_label.setStyleSheet("color: white;")
        title_layout.addWidget(title_label)

        version_label = QLabel(f"版本 {APP_VERSION} - 增强功能版")
        version_label.setFont(QFont("Microsoft YaHei", 12))
        version_label.setStyleSheet("color: #ECF0F1;")
        title_layout.addWidget(version_label)

        header_layout.addLayout(title_layout)
        header_layout.addStretch()

        # 功能说明
        features_label = QLabel("✨ 新功能: 单文件处理 | 实时监控 | BitComet集成")
        features_label.setFont(QFont("Microsoft YaHei", 11))
        features_label.setStyleSheet("color: #ECF0F1;")
        header_layout.addWidget(features_label)

        layout.addWidget(header_frame)

    def create_tabs(self, layout: QVBoxLayout):
        """创建标签页"""
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #BDC3C7;
                border-radius: 6px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #ECF0F1;
                color: #2C3E50;
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: #3498DB;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #D5DBDB;
            }
        """)

        # 磁力链接处理标签页
        magnet_tab = MagnetLinkWidget()
        self.tab_widget.addTab(magnet_tab, "🧲 磁力链接")

        # 单文件处理标签页
        single_file_tab = self.create_single_file_tab()
        self.tab_widget.addTab(single_file_tab, "📁 单文件处理")

        # 批量处理标签页（保留原有功能）
        batch_tab = self.create_batch_tab()
        self.tab_widget.addTab(batch_tab, "📦 批量处理")

        layout.addWidget(self.tab_widget)

    def create_single_file_tab(self) -> QWidget:
        """创建单文件处理标签页"""
        tab_widget = QWidget()
        layout = QHBoxLayout(tab_widget)

        # 使用QSplitter实现可调整大小的分割
        splitter = QSplitter(Qt.Orientation.Horizontal)
        splitter.setStyleSheet("""
            QSplitter::handle {
                background-color: #BDC3C7;
                width: 3px;
            }
            QSplitter::handle:hover {
                background-color: #3498DB;
            }
        """)

        # 左侧：文件列表
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(0, 0, 0, 0)

        self.xml_file_list = XMLFileListWidget()
        left_layout.addWidget(self.xml_file_list)

        splitter.addWidget(left_panel)

        # 右侧：结果显示
        self.single_file_result = SingleFileResultWidget()
        splitter.addWidget(self.single_file_result)

        # 设置初始比例 (左侧30%，右侧70%)
        splitter.setSizes([300, 700])
        splitter.setCollapsible(0, False)  # 左侧不可完全折叠
        splitter.setCollapsible(1, False)  # 右侧不可完全折叠

        layout.addWidget(splitter)

        # 连接信号
        self.xml_file_list.file_selected.connect(self.single_file_result.load_file)

        return tab_widget

    def create_batch_tab(self) -> QWidget:
        """创建批量处理标签页（简化版原有功能）"""
        tab_widget = QWidget()
        layout = QVBoxLayout(tab_widget)

        # 说明文字
        info_label = QLabel("📦 批量处理功能")
        info_label.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))
        info_label.setStyleSheet("color: #2C3E50; margin-bottom: 10px;")
        layout.addWidget(info_label)

        # 输入区域
        input_group = QGroupBox("输入ED2K链接")
        input_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #BDC3C7;
                border-radius: 6px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        input_layout = QVBoxLayout(input_group)

        self.batch_input = QPlainTextEdit()
        self.batch_input.setPlaceholderText("在此粘贴ED2K链接，每行一个...")
        self.batch_input.setStyleSheet("""
            QPlainTextEdit {
                border: 1px solid #BDC3C7;
                border-radius: 4px;
                padding: 8px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 14px;
                line-height: 1.4;
            }
        """)
        input_layout.addWidget(self.batch_input)

        # 处理按钮
        process_btn = QPushButton("🔄 处理链接")
        process_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498DB;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2980B9;
            }
        """)
        process_btn.clicked.connect(self.process_batch_links)
        input_layout.addWidget(process_btn)

        layout.addWidget(input_group)

        # 结果区域
        result_group = QGroupBox("处理结果")
        result_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #BDC3C7;
                border-radius: 6px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        result_layout = QVBoxLayout(result_group)

        self.batch_result = QTextBrowser()
        self.batch_result.setStyleSheet("""
            QTextBrowser {
                border: 1px solid #BDC3C7;
                border-radius: 4px;
                padding: 8px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 14px;
                line-height: 1.4;
            }
        """)
        result_layout.addWidget(self.batch_result)

        layout.addWidget(result_group)

        return tab_widget

    def process_batch_links(self):
        """处理批量链接"""
        text = self.batch_input.toPlainText().strip()
        if not text:
            QMessageBox.warning(self, "警告", "请输入ED2K链接")
            return

        lines = text.splitlines()
        video_links = []
        other_links = []

        for line in lines:
            line = line.strip()
            if not line or not line.startswith('ed2k://'):
                continue

            try:
                parts = line.split('|')
                if len(parts) >= 5 and parts[1] == 'file':
                    filename = urllib.parse.unquote(parts[2])
                    ext = os.path.splitext(filename)[1].lower()
                    if ext in VIDEO_EXTS:
                        video_links.append(line)
                    else:
                        other_links.append(line)
            except:
                continue

        # 显示结果
        html_content = f"<h3>📊 处理结果</h3>"
        html_content += f"<p>视频文件: {len(video_links)} 个</p>"
        html_content += f"<p>其他文件: {len(other_links)} 个</p>"

        if video_links:
            html_content += "<h4>📹 视频文件</h4><ul>"
            for link in video_links[:10]:  # 只显示前10个
                filename = self.extract_filename_from_link(link)
                html_content += f"<li>{filename}</li>"
            if len(video_links) > 10:
                html_content += f"<li>... 还有 {len(video_links) - 10} 个文件</li>"
            html_content += "</ul>"

        if other_links:
            html_content += "<h4>📄 其他文件</h4><ul>"
            for link in other_links[:10]:  # 只显示前10个
                filename = self.extract_filename_from_link(link)
                html_content += f"<li>{filename}</li>"
            if len(other_links) > 10:
                html_content += f"<li>... 还有 {len(other_links) - 10} 个文件</li>"
            html_content += "</ul>"

        self.batch_result.setHtml(html_content)

    def extract_filename_from_link(self, ed2k_link: str) -> str:
        """从ED2K链接提取文件名"""
        try:
            parts = ed2k_link.split('|')
            if len(parts) >= 3:
                return urllib.parse.unquote(parts[2])
        except:
            pass
        return "未知文件"

    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.status_bar.setStyleSheet("""
            QStatusBar {
                background-color: #ECF0F1;
                border-top: 1px solid #BDC3C7;
                padding: 5px;
            }
        """)
        self.status_bar.showMessage("就绪")
        self.setStatusBar(self.status_bar)

    def setup_shortcuts(self):
        """设置快捷键"""
        # Ctrl+Q 退出
        quit_action = QAction(self)
        quit_action.setShortcut(QKeySequence("Ctrl+Q"))
        quit_action.triggered.connect(self.close)
        self.addAction(quit_action)

        # F5 刷新
        refresh_action = QAction(self)
        refresh_action.setShortcut(QKeySequence("F5"))
        refresh_action.triggered.connect(self.refresh_all)
        self.addAction(refresh_action)

    def refresh_all(self):
        """刷新所有内容"""
        if hasattr(self, 'xml_file_list'):
            self.xml_file_list.refresh_file_list()
        self.status_bar.showMessage("已刷新", 2000)

    def setup_style(self):
        """设置整体样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #F8F9FA;
            }
        """)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setApplicationName(APP_NAME)
    app.setApplicationVersion(APP_VERSION)

    # 设置应用图标（如果有的话）
    # app.setWindowIcon(QIcon("icon.png"))

    window = EnhancedED2KMainWindow()
    window.show()

    sys.exit(app.exec())

if __name__ == "__main__":
    main()
