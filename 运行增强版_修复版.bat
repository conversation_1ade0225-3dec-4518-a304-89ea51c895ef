@echo off
chcp 65001 >nul
title ED2K 批量处理工具 Enhanced v6.0.0 - 修复版

echo.
echo ████████╗██████╗ ██████╗ ██╗  ██╗    ███████╗███╗   ██╗██╗  ██╗ █████╗ ███╗   ██╗ ██████╗███████╗██████╗ 
echo ██╔════╝██╔══██╗╚════██╗██║ ██╔╝    ██╔════╝████╗  ██║██║  ██║██╔══██╗████╗  ██║██╔════╝██╔════╝██╔══██╗
echo █████╗  ██║  ██║ █████╔╝█████╔╝     █████╗  ██╔██╗ ██║███████║███████║██╔██╗ ██║██║     █████╗  ██║  ██║
echo ██╔══╝  ██║  ██║██╔═══╝ ██╔═██╗     ██╔══╝  ██║╚██╗██║██╔══██║██╔══██║██║╚██╗██║██║     ██╔══╝  ██║  ██║
echo ███████╗██████╔╝███████╗██║  ██╗    ███████╗██║ ╚████║██║  ██║██║  ██║██║ ╚████║╚██████╗███████╗██████╔╝
echo ╚══════╝╚═════╝ ╚══════╝╚═╝  ╚═╝    ╚══════╝╚═╝  ╚═══╝╚═╝  ╚═╝╚═╝  ╚═╝╚═╝  ╚═══╝ ╚═════╝╚══════╝╚═════╝ 
echo.

echo ┌─────────────────────────────────────────────────────────────┐
echo │                    🔧 修复版启动器                          │
echo │                                                             │
echo │  本版本修复了以下问题：                                     │
echo │  ✅ 修复了BitComet控制器初始化问题                          │
echo │  ✅ 修复了ED2KProcessor类缺失问题                           │
echo │  ✅ 修复了配置管理器相关错误                                │
echo │  ✅ 改进了错误处理和异常捕获                                │
echo └─────────────────────────────────────────────────────────────┘

echo.
echo 🔍 正在检查运行环境...

REM 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未检测到Python，请先安装Python 3.8+
    echo 💡 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

REM 检查PyQt6是否安装
python -c "import PyQt6" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未检测到PyQt6，正在尝试安装...
    echo 📦 执行: pip install PyQt6
    pip install PyQt6
    if %errorlevel% neq 0 (
        echo ❌ PyQt6安装失败，请手动安装
        echo 💡 执行命令: pip install PyQt6
        pause
        exit /b 1
    )
)

echo ✅ PyQt6依赖检查通过

REM 检查主程序文件
if not exist "ed2k_enhanced_gui.py" (
    echo ❌ 未找到主程序文件 ed2k_enhanced_gui.py
    echo 💡 请确保文件在当前目录下
    pause
    exit /b 1
)

echo ✅ 主程序文件检查通过

echo.
echo 🎯 修复版功能说明：
echo ┌─────────────────────────────────────────────────────────────┐
echo │ 🧲 磁力链接处理  - 支持手动设置BitComet路径，自动保存配置   │
echo │ 📁 单文件处理    - 可拖动调整界面比例，自动保存布局         │
echo │ 🔄 批量处理      - 支持种子文件和磁力链接批量添加           │
echo │ 📊 智能分类      - 自动区分视频文件和其他文件               │
echo │ 💫 现代界面      - 更大字体，更易读的ED2K链接显示           │
echo │ 🔧 配置保存      - 自动保存用户设置，下次启动自动恢复       │
echo │ 🛠️ 错误修复      - 修复了导致闪退的各种问题                │
echo └─────────────────────────────────────────────────────────────┘

echo.
echo ┌─────────────────────────────────────────────────────────────┐
echo │                      使用提示                               │
echo ├─────────────────────────────────────────────────────────────┤
echo │ • 磁力链接：支持手动设置BitComet路径，配置自动保存          │
echo │ • 单文件处理：可拖动分割线调整窗口，布局自动保存           │
echo │ • 批量处理：粘贴种子路径或磁力链接，一键添加到BitComet     │
echo │ • 状态检查：实时监控ED2K生成状态，智能提示操作             │
echo │ • 快捷键：F5刷新，Ctrl+Q退出                               │
echo │ • 配置文件：ed2k_config.ini 自动保存所有设置               │
echo └─────────────────────────────────────────────────────────────┘

echo.
echo 🚀 正在启动程序...
echo.

REM 启动程序并捕获错误
python ed2k_enhanced_gui.py
set exit_code=%errorlevel%

echo.
if %exit_code% neq 0 (
    echo ❌ 程序异常退出 (错误代码: %exit_code%)
    echo.
    echo 🔧 故障排除建议：
    echo 1. 检查Python版本是否为3.8+
    echo 2. 重新安装PyQt6: pip install --upgrade PyQt6
    echo 3. 检查是否有杀毒软件阻止程序运行
    echo 4. 尝试以管理员身份运行此脚本
    echo 5. 检查系统是否缺少Visual C++运行库
    echo.
    echo 📝 如果问题持续，请提供以下信息：
    echo - Python版本: 
    python --version
    echo - PyQt6版本:
    python -c "import PyQt6; print('PyQt6 installed')" 2>nul || echo "PyQt6 not found"
    echo - 操作系统版本
    echo.
) else (
    echo ✅ 程序正常退出
)

echo.
echo 按任意键退出...
pause >nul
