@echo off
chcp 65001 >nul
title ED2K增强版工具 - 启动器

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                🚀 ED2K增强版工具启动器                       ║
echo ║                                                              ║
echo ║  新功能: 磁力链接推送 ^| 单文件处理 ^| 实时监控                ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:: 检查Python是否安装
echo 📋 检查运行环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python未安装！
    echo.
    echo 请先安装Python 3.8或更高版本：
    echo https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)
echo ✅ Python已安装

:: 检查PyQt6是否安装
echo 📦 检查PyQt6依赖...
python -c "import PyQt6" >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  PyQt6未安装，正在自动安装...
    pip install PyQt6
    if %errorlevel% neq 0 (
        echo ❌ PyQt6安装失败！
        echo.
        echo 请手动安装：pip install PyQt6
        pause
        exit /b 1
    )
    echo ✅ PyQt6安装完成
) else (
    echo ✅ PyQt6已安装
)

:: 检查增强版脚本是否存在
if not exist "ed2k_enhanced_gui.py" (
    echo ❌ 找不到增强版脚本文件！
    echo.
    echo 请确保 ed2k_enhanced_gui.py 文件在当前目录
    pause
    exit /b 1
)

:: 检查BitComet
echo 🔍 检查BitComet状态...
if exist "C:\Program Files\BitComet\BitComet.exe" (
    echo ✅ 找到BitComet: C:\Program Files\BitComet\BitComet.exe
) else if exist "C:\Program Files (x86)\BitComet\BitComet.exe" (
    echo ✅ 找到BitComet: C:\Program Files (x86)\BitComet\BitComet.exe
) else (
    echo ⚠️  未找到BitComet，磁力链接推送功能将不可用
    echo    如需使用此功能，请安装BitComet
)

:: 显示功能说明
echo.
echo 🎯 增强版功能说明：
echo ┌─────────────────────────────────────────────────────────────┐
echo │ 🧲 磁力链接处理  - 支持手动设置BitComet路径                 │
echo │ 📁 单文件处理    - 可拖动调整界面比例，更大显示区域         │
echo │ 🔄 实时监控      - 自动检测新生成的XML文件                  │
echo │ 📊 智能分类      - 自动区分视频文件和其他文件               │
echo │ 💫 现代界面      - 更大字体，更易读的ED2K链接显示           │
echo │ 🔧 界面优化      - 支持分割窗口拖动调整大小                 │
echo └─────────────────────────────────────────────────────────────┘
echo.

:: 询问是否启动
set /p start_app="是否启动ED2K增强版工具？(y/n): "
if /i not "%start_app%"=="y" (
    echo 已取消启动
    pause
    exit /b 0
)

:: 启动程序
echo.
echo 🚀 正在启动ED2K增强版工具...
echo.
echo ┌─────────────────────────────────────────────────────────────┐
echo │                      使用提示                               │
echo ├─────────────────────────────────────────────────────────────┤
echo │ • 磁力链接：支持手动设置BitComet路径，一键推送              │
echo │ • 单文件处理：可拖动中间分割线调整左右窗口大小             │
echo │ • 实时监控：新XML文件会自动出现在文件列表中                 │
echo │ • 字体优化：ED2K链接使用更大字体，更易阅读                 │
echo │ • 快捷键：F5刷新，Ctrl+Q退出                               │
echo └─────────────────────────────────────────────────────────────┘
echo.

python ed2k_enhanced_gui.py

:: 检查程序退出状态
if %errorlevel% neq 0 (
    echo.
    echo ❌ 程序运行出错！
    echo.
    echo 可能的解决方案：
    echo 1. 检查Python和PyQt6是否正确安装
    echo 2. 确保脚本文件完整无损
    echo 3. 以管理员权限运行此脚本
    echo 4. 检查系统是否有杀毒软件阻止
    echo.
) else (
    echo.
    echo ✅ 程序正常退出
)

echo.
echo 感谢使用ED2K增强版工具！
pause
