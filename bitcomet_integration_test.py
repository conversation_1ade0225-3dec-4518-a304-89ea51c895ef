#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BitComet集成测试脚本
专门测试BitComet磁力链接集成功能
"""

import os
import sys
import subprocess
import time
import tempfile
import webbrowser
from pathlib import Path

def test_bitcomet_basic(bitcomet_path):
    """测试BitComet基本启动"""
    print("🧪 测试BitComet基本启动...")
    
    try:
        # 只启动BitComet，不传递参数
        process = subprocess.Popen([bitcomet_path], shell=False)
        time.sleep(3)
        print("✅ BitComet基本启动成功")
        return True
    except Exception as e:
        print(f"❌ BitComet基本启动失败: {e}")
        return False

def test_method_clipboard(bitcomet_path, magnet_url):
    """方法1: 剪贴板方式"""
    print("\n🧪 测试方法1: 剪贴板方式")
    
    try:
        # 模拟复制到剪贴板（这里只是打印）
        print(f"📋 磁力链接: {magnet_url[:50]}...")
        
        # 启动BitComet
        process = subprocess.Popen([bitcomet_path], shell=False)
        time.sleep(3)
        
        print("✅ 方法1成功: BitComet已启动，需要手动粘贴磁力链接")
        print("💡 在BitComet中按 Ctrl+V 或点击'添加任务'按钮")
        return True
    except Exception as e:
        print(f"❌ 方法1失败: {e}")
        return False

def test_method_webbrowser(magnet_url):
    """方法2: 系统默认程序"""
    print("\n🧪 测试方法2: 系统默认程序")
    
    try:
        print("尝试使用系统默认程序打开磁力链接...")
        # 这里不实际执行，只是测试
        print("✅ 方法2理论上可行: 使用系统默认程序")
        return True
    except Exception as e:
        print(f"❌ 方法2失败: {e}")
        return False

def test_method_batch_file(bitcomet_path, magnet_url):
    """方法3: 临时批处理文件"""
    print("\n🧪 测试方法3: 临时批处理文件")
    
    try:
        # 创建临时批处理文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.bat', delete=False, encoding='utf-8') as f:
            f.write('@echo off\n')
            f.write(f'echo 启动BitComet...\n')
            f.write(f'start "" "{bitcomet_path}" "{magnet_url}"\n')
            f.write('echo 完成\n')
            f.write('pause\n')
            temp_bat = f.name
        
        print(f"📝 创建临时批处理文件: {temp_bat}")
        print("💡 批处理文件内容:")
        with open(temp_bat, 'r', encoding='utf-8') as f:
            print(f.read())
        
        # 询问是否执行
        choice = input("是否执行批处理文件? (y/n): ").lower()
        if choice == 'y':
            subprocess.run([temp_bat], shell=True)
            print("✅ 方法3执行完成")
        else:
            print("⏭️ 方法3跳过执行")
        
        # 清理临时文件
        try:
            os.unlink(temp_bat)
            print("🗑️ 临时文件已清理")
        except:
            pass
        
        return True
    except Exception as e:
        print(f"❌ 方法3失败: {e}")
        return False

def test_method_shell_command(bitcomet_path, magnet_url):
    """方法4: Shell命令"""
    print("\n🧪 测试方法4: Shell命令")
    
    try:
        cmd = f'start "" "{bitcomet_path}" "{magnet_url}"'
        print(f"📝 Shell命令: {cmd}")
        
        choice = input("是否执行Shell命令? (y/n): ").lower()
        if choice == 'y':
            os.system(cmd)
            print("✅ 方法4执行完成")
        else:
            print("⏭️ 方法4跳过执行")
        
        return True
    except Exception as e:
        print(f"❌ 方法4失败: {e}")
        return False

def analyze_bitcomet_version(bitcomet_path):
    """分析BitComet版本信息"""
    print("\n🔍 分析BitComet版本信息...")
    
    try:
        # 获取文件属性
        file_path = Path(bitcomet_path)
        if file_path.exists():
            stat = file_path.stat()
            print(f"📁 文件大小: {stat.st_size / (1024*1024):.1f} MB")
            print(f"📅 修改时间: {time.ctime(stat.st_mtime)}")
            
            # 尝试获取版本信息
            try:
                result = subprocess.run([bitcomet_path, '--version'], 
                                      capture_output=True, text=True, timeout=5)
                if result.stdout:
                    print(f"📋 版本信息: {result.stdout}")
            except:
                print("⚠️ 无法获取版本信息")
            
            return True
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 BitComet集成测试脚本")
    print("=" * 60)
    
    # 获取BitComet路径
    default_path = r"D:\迅雷下载\比特彗星(BitComet)-v2.06 隐藏功能解锁版\比特彗星(BitComet)-v2.06\BitComet_x64.exe"
    
    bitcomet_path = input(f"请输入BitComet路径 (回车使用默认路径):\n{default_path}\n> ").strip()
    
    if not bitcomet_path:
        bitcomet_path = default_path
    
    print(f"\n📋 使用路径: {bitcomet_path}")
    
    # 检查路径
    if not os.path.exists(bitcomet_path):
        print("❌ BitComet路径不存在！")
        return
    
    # 测试磁力链接
    test_magnet = "magnet:?xt=urn:btih:f2b9671662f7c3ed61c33a8f1be828098017430c&dn=测试文件"
    print(f"📋 测试磁力链接: {test_magnet[:50]}...")
    
    print("\n" + "="*60)
    
    # 分析BitComet
    analyze_bitcomet_version(bitcomet_path)
    
    # 基本启动测试
    if not test_bitcomet_basic(bitcomet_path):
        print("❌ BitComet基本启动失败，无法继续测试")
        return
    
    # 等待用户确认
    input("\n请检查BitComet是否已启动，然后按回车继续...")
    
    # 测试各种方法
    methods = [
        ("剪贴板方式", lambda: test_method_clipboard(bitcomet_path, test_magnet)),
        ("系统默认程序", lambda: test_method_webbrowser(test_magnet)),
        ("临时批处理文件", lambda: test_method_batch_file(bitcomet_path, test_magnet)),
        ("Shell命令", lambda: test_method_shell_command(bitcomet_path, test_magnet)),
    ]
    
    results = []
    
    for method_name, test_func in methods:
        print(f"\n{'='*20} {method_name} {'='*20}")
        
        try:
            success = test_func()
            results.append((method_name, success))
            
            if success:
                print(f"✅ {method_name} 测试完成")
            else:
                print(f"❌ {method_name} 测试失败")
                
        except Exception as e:
            print(f"❌ {method_name} 异常: {e}")
            results.append((method_name, False))
        
        # 等待用户确认
        input("\n按回车键继续下一个测试...")
    
    # 显示测试结果
    print("\n" + "="*60)
    print("📊 测试结果总结:")
    print("-" * 60)
    
    for method_name, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{method_name}: {status}")
    
    # 推荐方案
    successful_methods = [name for name, success in results if success]
    
    print(f"\n💡 推荐解决方案:")
    print("1. 使用剪贴板方式最稳定")
    print("2. 启动BitComet后手动粘贴磁力链接")
    print("3. 在程序中提供清晰的操作指导")
    print("4. 考虑添加'一键复制磁力链接'功能")
    
    print(f"\n🔧 程序改进建议:")
    print("1. 优先使用剪贴板方式")
    print("2. 提供详细的操作步骤说明")
    print("3. 添加BitComet启动状态检测")
    print("4. 考虑支持其他下载工具")
    
    print("\n按回车键退出...")
    input()

if __name__ == "__main__":
    main()
