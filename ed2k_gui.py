import os
import re
import xml.etree.ElementTree as ET
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from tkinter.scrolledtext import ScrolledText
import urllib.parse
from pathlib import Path
import threading
import time
from typing import List, Tuple, Dict

APP_VERSION = "4.0.0"
APP_NAME = "ED2K 批量处理工具"
VIDEO_EXTS = {'.mp4', '.mkv', '.avi', '.mov', '.wmv', '.flv', '.webm'}
AD_PATTERN = re.compile(r"【更多.*?】|更多无水印.*?】|更多无水印.*?\.|www\.[^\s]+|BBQDDQ|BPHDTV", re.IGNORECASE)

# macOS/iOS 风格主题配置
class ModernTheme:
    # 液态玻璃效果颜色
    GLASS_BG = "#F2F2F7"  # iOS 系统背景色
    GLASS_SECONDARY = "#FFFFFF"  # 卡片背景
    GLASS_TERTIARY = "#F2F2F7"  # 三级背景

    # 文字颜色
    TEXT_PRIMARY = "#000000"  # 主要文字
    TEXT_SECONDARY = "#3C3C43"  # 次要文字
    TEXT_TERTIARY = "#8E8E93"  # 三级文字

    # 系统颜色
    BLUE = "#007AFF"  # 系统蓝色
    GREEN = "#34C759"  # 系统绿色
    RED = "#FF3B30"  # 系统红色
    ORANGE = "#FF9500"  # 系统橙色
    PURPLE = "#AF52DE"  # 系统紫色
    CYAN = "#5AC8FA"  # 系统青色

    # 渐变色
    GRADIENT_START = "#007AFF"
    GRADIENT_END = "#5AC8FA"

    # 字体配置
    FONT_LARGE = ("SF Pro Display", 16, "normal")
    FONT_MEDIUM = ("SF Pro Text", 14, "normal")
    FONT_SMALL = ("SF Pro Text", 12, "normal")
    FONT_CAPTION = ("SF Pro Text", 11, "normal")
    FONT_MONO = ("SF Mono", 11, "normal")

class ModernButton(tk.Button):
    """现代化按钮组件，支持悬停效果和圆角样式"""
    def __init__(self, parent, text="", command=None, style="primary", **kwargs):
        self.style = style
        self.original_bg = self._get_style_color(style)
        self.hover_bg = self._get_hover_color(style)

        super().__init__(
            parent,
            text=text,
            command=command,
            font=ModernTheme.FONT_MEDIUM,
            bg=self.original_bg,
            fg="white",
            relief=tk.FLAT,
            bd=0,
            padx=20,
            pady=8,
            cursor="hand2",
            **kwargs
        )

        self.bind("<Enter>", self._on_enter)
        self.bind("<Leave>", self._on_leave)
        self.bind("<Button-1>", self._on_click)
        self.bind("<ButtonRelease-1>", self._on_release)

    def _get_style_color(self, style):
        colors = {
            "primary": ModernTheme.BLUE,
            "success": ModernTheme.GREEN,
            "danger": ModernTheme.RED,
            "warning": ModernTheme.ORANGE,
            "info": ModernTheme.CYAN,
            "secondary": ModernTheme.PURPLE
        }
        return colors.get(style, ModernTheme.BLUE)

    def _get_hover_color(self, style):
        # 悬停时颜色稍微变暗
        base_color = self._get_style_color(style)
        return self._darken_color(base_color)

    def _darken_color(self, color):
        # 简单的颜色变暗算法
        if color == ModernTheme.BLUE: return "#0056CC"
        elif color == ModernTheme.GREEN: return "#28A745"
        elif color == ModernTheme.RED: return "#DC3545"
        elif color == ModernTheme.ORANGE: return "#E8890B"
        elif color == ModernTheme.CYAN: return "#17A2B8"
        elif color == ModernTheme.PURPLE: return "#9A42C8"
        return color

    def _on_enter(self, event):
        self.config(bg=self.hover_bg)

    def _on_leave(self, event):
        self.config(bg=self.original_bg)

    def _on_click(self, event):
        self.config(relief=tk.SUNKEN)

    def _on_release(self, event):
        self.config(relief=tk.FLAT)

class ProgressDialog:
    """现代化进度对话框"""
    def __init__(self, parent, title="处理中...", message="请稍候..."):
        self.parent = parent
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x150")
        self.dialog.resizable(False, False)
        self.dialog.configure(bg=ModernTheme.GLASS_BG)

        # 居中显示
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 创建内容
        main_frame = tk.Frame(self.dialog, bg=ModernTheme.GLASS_BG)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 消息标签
        self.message_label = tk.Label(
            main_frame,
            text=message,
            font=ModernTheme.FONT_MEDIUM,
            bg=ModernTheme.GLASS_BG,
            fg=ModernTheme.TEXT_PRIMARY
        )
        self.message_label.pack(pady=(0, 15))

        # 进度条
        self.progress = ttk.Progressbar(
            main_frame,
            mode='indeterminate',
            length=300
        )
        self.progress.pack(pady=(0, 15))
        self.progress.start(10)

        # 状态标签
        self.status_label = tk.Label(
            main_frame,
            text="",
            font=ModernTheme.FONT_SMALL,
            bg=ModernTheme.GLASS_BG,
            fg=ModernTheme.TEXT_SECONDARY
        )
        self.status_label.pack()

        # 居中窗口
        self._center_window()

    def _center_window(self):
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (150 // 2)
        self.dialog.geometry(f"400x150+{x}+{y}")

    def update_status(self, status):
        self.status_label.config(text=status)
        self.dialog.update()

    def close(self):
        self.progress.stop()
        self.dialog.destroy()

class Ed2kBatchApp:
    def __init__(self, root):
        self.root = root
        self.root.title(f"{APP_NAME} v{APP_VERSION}")
        self.root.geometry("900x700")
        self.root.minsize(800, 600)
        self.root.configure(bg=ModernTheme.GLASS_BG)

        # 设置窗口图标和样式
        self._setup_window_style()

        # 初始化数据
        self.video_links: List[str] = []
        self.other_links: List[str] = []
        self.xml_file_links: Dict[str, Tuple[List[str], List[str]]] = {}
        self.processing = False

        # 创建UI
        self._create_ui()

        # 绑定快捷键
        self._bind_shortcuts()

    def _setup_window_style(self):
        """设置窗口样式"""
        # 配置ttk样式
        style = ttk.Style()
        style.theme_use('clam')

        # 自定义进度条样式
        style.configure(
            "Modern.Horizontal.TProgressbar",
            background=ModernTheme.BLUE,
            troughcolor=ModernTheme.GLASS_TERTIARY,
            borderwidth=0,
            lightcolor=ModernTheme.BLUE,
            darkcolor=ModernTheme.BLUE
        )

    def _create_ui(self):
        """创建现代化UI界面"""
        # 主容器
        main_container = tk.Frame(self.root, bg=ModernTheme.GLASS_BG)
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 标题区域
        self._create_header(main_container)

        # 输入区域
        self._create_input_section(main_container)

        # 操作按钮区域
        self._create_action_buttons(main_container)

        # 结果显示区域
        self._create_result_section(main_container)

        # 状态栏
        self._create_status_bar()

    def _create_header(self, parent):
        """创建标题区域"""
        header_frame = tk.Frame(parent, bg=ModernTheme.GLASS_BG)
        header_frame.pack(fill=tk.X, pady=(0, 20))

        title_label = tk.Label(
            header_frame,
            text=APP_NAME,
            font=("SF Pro Display", 24, "bold"),
            bg=ModernTheme.GLASS_BG,
            fg=ModernTheme.TEXT_PRIMARY
        )
        title_label.pack(side=tk.LEFT)

        version_label = tk.Label(
            header_frame,
            text=f"v{APP_VERSION}",
            font=ModernTheme.FONT_SMALL,
            bg=ModernTheme.GLASS_BG,
            fg=ModernTheme.TEXT_TERTIARY
        )
        version_label.pack(side=tk.RIGHT, anchor=tk.E)

    def _create_input_section(self, parent):
        """创建输入区域"""
        input_frame = tk.Frame(parent, bg=ModernTheme.GLASS_SECONDARY, relief=tk.FLAT, bd=1)
        input_frame.pack(fill=tk.X, pady=(0, 15))

        # 添加内边距
        input_container = tk.Frame(input_frame, bg=ModernTheme.GLASS_SECONDARY)
        input_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # 标签
        input_label = tk.Label(
            input_container,
            text="输入磁力/ED2K链接或处理XML目录",
            font=ModernTheme.FONT_MEDIUM,
            bg=ModernTheme.GLASS_SECONDARY,
            fg=ModernTheme.TEXT_PRIMARY
        )
        input_label.pack(anchor=tk.W, pady=(0, 8))

        # 输入文本框
        self.input_text = ScrolledText(
            input_container,
            height=6,
            font=ModernTheme.FONT_MONO,
            wrap=tk.WORD,
            bd=1,
            relief=tk.SOLID,
            bg="white",
            fg=ModernTheme.TEXT_PRIMARY,
            insertbackground=ModernTheme.BLUE,
            selectbackground=ModernTheme.BLUE,
            selectforeground="white"
        )
        self.input_text.pack(fill=tk.X)

    def _create_action_buttons(self, parent):
        """创建操作按钮区域"""
        btn_frame = tk.Frame(parent, bg=ModernTheme.GLASS_BG)
        btn_frame.pack(fill=tk.X, pady=(0, 15))

        # 第一行按钮
        btn_row1 = tk.Frame(btn_frame, bg=ModernTheme.GLASS_BG)
        btn_row1.pack(fill=tk.X, pady=(0, 8))

        self.convert_btn = ModernButton(
            btn_row1,
            text="🔄 转换文本",
            command=self.process_input,
            style="primary"
        )
        self.convert_btn.pack(side=tk.LEFT, padx=(0, 8))

        self.process_xml_btn = ModernButton(
            btn_row1,
            text="📁 处理XML目录",
            command=self.process_folder,
            style="info"
        )
        self.process_xml_btn.pack(side=tk.LEFT, padx=(0, 8))

        self.clear_xml_btn = ModernButton(
            btn_row1,
            text="🗑️ 清除XML",
            command=self.clear_xml_files,
            style="danger"
        )
        self.clear_xml_btn.pack(side=tk.LEFT)

        # 第二行按钮
        btn_row2 = tk.Frame(btn_frame, bg=ModernTheme.GLASS_BG)
        btn_row2.pack(fill=tk.X)

        self.copy_video_btn = ModernButton(
            btn_row2,
            text="📹 复制视频链接",
            command=self.copy_video_links,
            style="success"
        )
        self.copy_video_btn.pack(side=tk.LEFT, padx=(0, 8))

        self.copy_all_btn = ModernButton(
            btn_row2,
            text="📋 复制全部链接",
            command=self.copy_all_links,
            style="secondary"
        )
        self.copy_all_btn.pack(side=tk.LEFT)

    def _create_result_section(self, parent):
        """创建结果显示区域"""
        result_frame = tk.Frame(parent, bg=ModernTheme.GLASS_SECONDARY, relief=tk.FLAT, bd=1)
        result_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        # 添加内边距
        result_container = tk.Frame(result_frame, bg=ModernTheme.GLASS_SECONDARY)
        result_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # 标签
        result_label = tk.Label(
            result_container,
            text="转换结果",
            font=ModernTheme.FONT_MEDIUM,
            bg=ModernTheme.GLASS_SECONDARY,
            fg=ModernTheme.TEXT_PRIMARY
        )
        result_label.pack(anchor=tk.W, pady=(0, 8))

        # 结果文本框
        self.result_text = ScrolledText(
            result_container,
            font=ModernTheme.FONT_MONO,
            height=16,
            wrap=tk.NONE,
            bd=1,
            relief=tk.SOLID,
            bg="white",
            fg=ModernTheme.TEXT_PRIMARY,
            insertbackground=ModernTheme.BLUE,
            selectbackground=ModernTheme.BLUE,
            selectforeground="white"
        )
        self.result_text.pack(fill=tk.BOTH, expand=True)

    def _create_status_bar(self):
        """创建状态栏"""
        self.status_var = tk.StringVar()
        self.status_var.set("🟢 准备就绪")

        status_bar = tk.Label(
            self.root,
            textvariable=self.status_var,
            font=ModernTheme.FONT_CAPTION,
            anchor=tk.W,
            bg=ModernTheme.GLASS_TERTIARY,
            fg=ModernTheme.TEXT_SECONDARY,
            padx=20,
            pady=8
        )
        status_bar.pack(fill=tk.X, side=tk.BOTTOM)

    def _bind_shortcuts(self):
        """绑定快捷键"""
        self.root.bind('<Control-Return>', lambda e: self.process_input())
        self.root.bind('<Control-o>', lambda e: self.process_folder())
        self.root.bind('<Control-v>', lambda e: self.copy_video_links())
        self.root.bind('<Control-a>', lambda e: self.copy_all_links())
    def clean_filename(self, filename: str) -> str:
        """清理文件名，移除广告内容"""
        cleaned = AD_PATTERN.sub("", filename).strip()
        return cleaned

    def process_input(self):
        """处理输入的文本链接"""
        if self.processing:
            return

        text = self.input_text.get("1.0", tk.END).strip()
        if not text:
            self.status_var.set("⚠️ 请输入要处理的链接")
            return

        self.processing = True
        self._disable_buttons()

        # 在后台线程中处理
        threading.Thread(target=self._process_input_worker, args=(text,), daemon=True).start()

    def _process_input_worker(self, text: str):
        """在后台线程中处理输入"""
        try:
            lines = text.splitlines()
            video_links, other_links = [], []
            total_lines = len(lines)

            # 创建进度对话框
            progress_dialog = ProgressDialog(self.root, "处理链接", "正在解析链接...")

            for i, line in enumerate(lines):
                line = line.strip()
                if not line or not line.startswith('ed2k://'):
                    continue

                if self.is_ad_ed2k(line):
                    continue

                try:
                    parts = line.split('|')
                    if len(parts) >= 5 and parts[1] == 'file':
                        filename = urllib.parse.unquote(parts[2])
                        ext = os.path.splitext(filename)[1].lower()
                        if ext in VIDEO_EXTS:
                            video_links.append(line)
                        else:
                            other_links.append(line)
                except Exception:
                    continue

                # 更新进度
                if i % 10 == 0:  # 每10行更新一次进度
                    progress = int((i / total_lines) * 100)
                    self.root.after(0, lambda p=progress: progress_dialog.update_status(f"已处理 {p}%"))

            # 在主线程中更新UI
            self.root.after(0, lambda: self._process_input_complete(video_links, other_links, progress_dialog))

        except Exception as e:
            self.root.after(0, lambda: self._process_error(f"处理失败: {str(e)}", progress_dialog))

    def _process_input_complete(self, video_links: List[str], other_links: List[str], progress_dialog):
        """处理完成后的UI更新"""
        try:
            progress_dialog.close()

            self.video_links = video_links
            self.other_links = other_links
            self.display_links()

            # 更新状态
            total_count = len(video_links) + len(other_links)
            if total_count > 0:
                self.status_var.set(f"✅ 转换成功！视频 {len(video_links)} 条，其他 {len(other_links)} 条")
            else:
                self.status_var.set("⚠️ 未找到有效的ED2K链接")

        finally:
            self.processing = False
            self._enable_buttons()

    def _process_error(self, error_msg: str, progress_dialog):
        """处理错误"""
        try:
            progress_dialog.close()
            self.status_var.set(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)
        finally:
            self.processing = False
            self._enable_buttons()

    def _disable_buttons(self):
        """禁用所有按钮"""
        buttons = [
            self.convert_btn, self.process_xml_btn, self.clear_xml_btn,
            self.copy_video_btn, self.copy_all_btn
        ]
        for btn in buttons:
            btn.config(state=tk.DISABLED)

    def _enable_buttons(self):
        """启用所有按钮"""
        buttons = [
            self.convert_btn, self.process_xml_btn, self.clear_xml_btn,
            self.copy_video_btn, self.copy_all_btn
        ]
        for btn in buttons:
            btn.config(state=tk.NORMAL)
    def process_folder(self):
        """处理XML目录"""
        if self.processing:
            return

        self.processing = True
        self._disable_buttons()

        # 在后台线程中处理
        threading.Thread(target=self._process_folder_worker, daemon=True).start()

    def _process_folder_worker(self):
        """在后台线程中处理XML文件"""
        try:
            folder = os.path.dirname(os.path.abspath(__file__))
            xml_files = list(Path(folder).glob('**/*.xml'))

            if not xml_files:
                self.root.after(0, lambda: self._process_folder_complete({}, "⚠️ 当前目录下没有找到XML文件"))
                return

            # 创建进度对话框
            progress_dialog = ProgressDialog(self.root, "处理XML文件", f"找到 {len(xml_files)} 个XML文件...")

            xml_file_links = {}
            total_files = len(xml_files)

            for i, xml_file in enumerate(xml_files):
                try:
                    # 更新进度
                    progress = int((i / total_files) * 100)
                    self.root.after(0, lambda p=progress, name=xml_file.name:
                                  progress_dialog.update_status(f"处理 {name} ({p}%)"))

                    tree = ET.parse(xml_file)
                    root = tree.getroot()
                    file_list = root.find('FileList')
                    v_links, o_links = [], []

                    if file_list is not None:
                        file_entries = file_list.findall('FileEntry')
                        for j, file_entry in enumerate(file_entries):
                            relative_path_name = file_entry.get('RelativePathName')
                            if not relative_path_name:
                                continue

                            if self.is_ad_filename(relative_path_name):
                                continue

                            ext = os.path.splitext(relative_path_name)[1].lower()
                            size = file_entry.get('Size')
                            e_mule_hash = file_entry.get('eMuleHash')

                            if not all([relative_path_name, size, e_mule_hash]):
                                continue

                            if relative_path_name.lower().startswith('ed2k:'):
                                continue

                            cleaned_name = self.clean_filename(relative_path_name)
                            e_mule_hash = e_mule_hash.upper()
                            encoded_filename = urllib.parse.quote(cleaned_name)
                            ed2k_link = f'ed2k://|file|{encoded_filename}|{size}|{e_mule_hash}|/'

                            if ext in VIDEO_EXTS:
                                v_links.append(ed2k_link)
                            else:
                                o_links.append(ed2k_link)

                    xml_file_links[xml_file.name] = [v_links, o_links]

                except Exception as e:
                    # 记录错误但继续处理其他文件
                    print(f"处理文件 {xml_file.name} 时出错: {e}")
                    continue

            # 计算总数
            total_v = sum(len(v) for v, _ in xml_file_links.values())
            total_o = sum(len(o) for _, o in xml_file_links.values())

            status_msg = f"✅ 目录处理完成！视频 {total_v} 条，其他 {total_o} 条"

            # 在主线程中更新UI
            self.root.after(0, lambda: self._process_folder_complete(xml_file_links, status_msg, progress_dialog))

        except Exception as e:
            self.root.after(0, lambda: self._process_error(f"处理XML目录失败: {str(e)}", progress_dialog))

    def _process_folder_complete(self, xml_file_links: Dict, status_msg: str, progress_dialog=None):
        """XML处理完成后的UI更新"""
        try:
            if progress_dialog:
                progress_dialog.close()

            self.xml_file_links = xml_file_links
            self.display_xml_links()
            self.status_var.set(status_msg)

        finally:
            self.processing = False
            self._enable_buttons()
    def display_links(self):
        """显示转换后的链接"""
        self.result_text.delete("1.0", tk.END)

        if self.video_links:
            self.result_text.insert(tk.END, "🎬 视频文件 ED2K 链接\n", "video_title")
            self.result_text.insert(tk.END, "=" * 50 + "\n", "separator")
            for i, link in enumerate(self.video_links, 1):
                self.result_text.insert(tk.END, f"{i:2d}. ", "number")
                self.result_text.insert(tk.END, link + "\n", "video")
            self.result_text.insert(tk.END, "\n", "spacing")

        if self.other_links:
            self.result_text.insert(tk.END, "📄 其他文件 ED2K 链接\n", "other_title")
            self.result_text.insert(tk.END, "=" * 50 + "\n", "separator")
            for i, link in enumerate(self.other_links, 1):
                self.result_text.insert(tk.END, f"{i:2d}. ", "number")
                self.result_text.insert(tk.END, link + "\n", "other")

        if not self.video_links and not self.other_links:
            self.result_text.insert(tk.END, "暂无转换结果\n", "empty")
            self.result_text.insert(tk.END, "请在上方输入框中粘贴 ED2K 链接，然后点击\"转换文本\"按钮。", "hint")

        self._configure_text_tags()

    def _configure_text_tags(self):
        """配置文本标签样式"""
        self.result_text.tag_config("video_title",
                                   foreground=ModernTheme.BLUE,
                                   font=("SF Pro Text", 14, "bold"))
        self.result_text.tag_config("other_title",
                                   foreground=ModernTheme.RED,
                                   font=("SF Pro Text", 14, "bold"))
        self.result_text.tag_config("separator",
                                   foreground=ModernTheme.TEXT_TERTIARY)
        self.result_text.tag_config("number",
                                   foreground=ModernTheme.TEXT_SECONDARY,
                                   font=("SF Mono", 10, "bold"))
        self.result_text.tag_config("video",
                                   foreground=ModernTheme.GREEN)
        self.result_text.tag_config("other",
                                   foreground=ModernTheme.ORANGE)
        self.result_text.tag_config("empty",
                                   foreground=ModernTheme.TEXT_TERTIARY,
                                   font=("SF Pro Text", 12, "italic"))
        self.result_text.tag_config("hint",
                                   foreground=ModernTheme.TEXT_SECONDARY,
                                   font=("SF Pro Text", 11))
        self.result_text.tag_config("xml_title",
                                   foreground=ModernTheme.PURPLE,
                                   font=("SF Pro Text", 13, "bold"))
        self.result_text.tag_config("spacing", font=("SF Pro Text", 6))
    def display_xml_links(self):
        """显示XML文件中的链接"""
        self.result_text.delete("1.0", tk.END)

        if not self.xml_file_links:
            self.result_text.insert(tk.END, "暂无XML处理结果\n", "empty")
            self.result_text.insert(tk.END, "请点击\"处理XML目录\"按钮来扫描当前目录下的XML文件。", "hint")
            self._configure_text_tags()
            return

        for xml_name, (v_links, o_links) in self.xml_file_links.items():
            # XML文件标题
            self.result_text.insert(tk.END, f"📁 {xml_name}\n", "xml_title")
            self.result_text.insert(tk.END, "─" * 60 + "\n", "separator")

            # 视频链接
            if v_links:
                self.result_text.insert(tk.END, "🎬 视频文件\n", "video_title")
                for i, link in enumerate(v_links, 1):
                    self.result_text.insert(tk.END, f"  {i:2d}. ", "number")
                    self.result_text.insert(tk.END, link + "\n", "video")

                # 添加复制按钮
                copy_btn = ModernButton(
                    self.result_text,
                    text=f"📋 复制 {xml_name} 视频链接",
                    command=lambda n=xml_name: self.copy_xml_video_links(n),
                    style="success"
                )
                copy_btn.config(font=("SF Pro Text", 10))
                self.result_text.window_create(tk.END, window=copy_btn)
                self.result_text.insert(tk.END, "\n\n", "spacing")

            # 其他文件链接
            if o_links:
                self.result_text.insert(tk.END, "📄 其他文件\n", "other_title")
                for i, link in enumerate(o_links, 1):
                    self.result_text.insert(tk.END, f"  {i:2d}. ", "number")
                    self.result_text.insert(tk.END, link + "\n", "other")
                self.result_text.insert(tk.END, "\n", "spacing")

            # 文件分隔符
            if xml_name != list(self.xml_file_links.keys())[-1]:  # 不是最后一个文件
                self.result_text.insert(tk.END, "\n" + "═" * 80 + "\n\n", "file_separator")

        self._configure_text_tags()

        # 添加额外的标签配置
        self.result_text.tag_config("file_separator",
                                   foreground=ModernTheme.TEXT_TERTIARY,
                                   font=("SF Pro Text", 8))
    def copy_video_links(self):
        """复制视频链接到剪贴板"""
        links = self.video_links if self.video_links else []
        if not links and self.xml_file_links:
            links = []
            for v, _ in self.xml_file_links.values():
                links.extend(v)

        if links:
            try:
                self.root.clipboard_clear()
                self.root.clipboard_append("\n".join(links))
                self.status_var.set(f"📋 已复制 {len(links)} 条视频链接到剪贴板")

                # 显示成功动画效果
                self._show_copy_success("视频链接")
            except Exception as e:
                self.status_var.set(f"❌ 复制失败: {str(e)}")
        else:
            self.status_var.set("⚠️ 没有可复制的视频链接")

    def copy_all_links(self):
        """复制所有链接到剪贴板"""
        links = self.video_links + self.other_links
        if not links and self.xml_file_links:
            links = []
            for v, o in self.xml_file_links.values():
                links.extend(v)
                links.extend(o)

        if links:
            try:
                self.root.clipboard_clear()
                self.root.clipboard_append("\n".join(links))
                video_count = len(self.video_links) if self.video_links else sum(len(v) for v, _ in self.xml_file_links.values())
                other_count = len(self.other_links) if self.other_links else sum(len(o) for _, o in self.xml_file_links.values())
                self.status_var.set(f"📋 已复制全部链接：视频 {video_count} 条，其他 {other_count} 条")

                # 显示成功动画效果
                self._show_copy_success("全部链接")
            except Exception as e:
                self.status_var.set(f"❌ 复制失败: {str(e)}")
        else:
            self.status_var.set("⚠️ 没有可复制的链接")

    def copy_xml_video_links(self, xml_name: str):
        """复制指定XML文件的视频链接"""
        v_links = self.xml_file_links.get(xml_name, [[], []])[0]
        if v_links:
            try:
                self.root.clipboard_clear()
                self.root.clipboard_append("\n".join(v_links))
                self.status_var.set(f"📋 已复制 {xml_name} 的 {len(v_links)} 条视频链接")

                # 显示成功动画效果
                self._show_copy_success(f"{xml_name} 视频链接")
            except Exception as e:
                self.status_var.set(f"❌ 复制失败: {str(e)}")
        else:
            self.status_var.set(f"⚠️ {xml_name} 没有可复制的视频链接")

    def _show_copy_success(self, content_type: str):
        """显示复制成功的动画效果"""
        # 创建一个临时的成功提示
        success_label = tk.Label(
            self.root,
            text=f"✅ {content_type} 已复制！",
            font=("SF Pro Text", 12, "bold"),
            bg=ModernTheme.GREEN,
            fg="white",
            padx=20,
            pady=10
        )

        # 计算居中位置
        self.root.update_idletasks()
        x = (self.root.winfo_width() // 2) - 100
        y = (self.root.winfo_height() // 2) - 20

        success_label.place(x=x, y=y)

        # 2秒后自动消失
        self.root.after(2000, success_label.destroy)
    def clear_xml_files(self):
        """清理XML文件"""
        folder = os.path.dirname(os.path.abspath(__file__))
        xml_files = list(Path(folder).glob('**/*.xml'))

        if not xml_files:
            self.status_var.set("⚠️ 当前目录下没有XML文件")
            messagebox.showinfo("提示", "当前目录下没有找到XML文件。")
            return

        # 创建现代化的确认对话框
        confirm_dialog = tk.Toplevel(self.root)
        confirm_dialog.title("确认删除")
        confirm_dialog.geometry("400x200")
        confirm_dialog.resizable(False, False)
        confirm_dialog.configure(bg=ModernTheme.GLASS_BG)
        confirm_dialog.transient(self.root)
        confirm_dialog.grab_set()

        # 居中显示
        confirm_dialog.update_idletasks()
        x = (confirm_dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (confirm_dialog.winfo_screenheight() // 2) - (200 // 2)
        confirm_dialog.geometry(f"400x200+{x}+{y}")

        # 内容框架
        content_frame = tk.Frame(confirm_dialog, bg=ModernTheme.GLASS_BG)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 警告图标和标题
        title_frame = tk.Frame(content_frame, bg=ModernTheme.GLASS_BG)
        title_frame.pack(fill=tk.X, pady=(0, 15))

        tk.Label(
            title_frame,
            text="⚠️ 确认删除操作",
            font=("SF Pro Text", 16, "bold"),
            bg=ModernTheme.GLASS_BG,
            fg=ModernTheme.RED
        ).pack()

        # 消息内容
        message_text = f"即将删除当前目录下的 {len(xml_files)} 个XML文件。\n\n此操作不可撤销，请确认是否继续？"
        tk.Label(
            content_frame,
            text=message_text,
            font=ModernTheme.FONT_MEDIUM,
            bg=ModernTheme.GLASS_BG,
            fg=ModernTheme.TEXT_PRIMARY,
            justify=tk.CENTER,
            wraplength=350
        ).pack(pady=(0, 20))

        # 按钮框架
        btn_frame = tk.Frame(content_frame, bg=ModernTheme.GLASS_BG)
        btn_frame.pack(fill=tk.X)

        result = {"confirmed": False}

        def on_confirm():
            result["confirmed"] = True
            confirm_dialog.destroy()

        def on_cancel():
            result["confirmed"] = False
            confirm_dialog.destroy()

        # 取消按钮
        cancel_btn = ModernButton(
            btn_frame,
            text="取消",
            command=on_cancel,
            style="secondary"
        )
        cancel_btn.pack(side=tk.RIGHT, padx=(8, 0))

        # 确认按钮
        confirm_btn = ModernButton(
            btn_frame,
            text="确认删除",
            command=on_confirm,
            style="danger"
        )
        confirm_btn.pack(side=tk.RIGHT)

        # 等待用户选择
        self.root.wait_window(confirm_dialog)

        if result["confirmed"]:
            self._perform_xml_cleanup(xml_files)
        else:
            self.status_var.set("🚫 已取消删除操作")

    def _perform_xml_cleanup(self, xml_files: List[Path]):
        """执行XML文件清理"""
        try:
            # 创建进度对话框
            progress_dialog = ProgressDialog(self.root, "删除XML文件", "正在删除文件...")

            deleted_count = 0
            total_files = len(xml_files)

            for i, xml_file in enumerate(xml_files):
                try:
                    os.remove(xml_file)
                    deleted_count += 1

                    # 更新进度
                    progress = int((i / total_files) * 100)
                    progress_dialog.update_status(f"已删除 {deleted_count}/{total_files} 个文件 ({progress}%)")

                except Exception as e:
                    print(f"删除文件 {xml_file} 失败: {e}")
                    continue

            progress_dialog.close()

            # 清理内存中的数据
            self.xml_file_links = {}
            self.result_text.delete("1.0", tk.END)

            if deleted_count == total_files:
                self.status_var.set(f"✅ 已成功删除 {deleted_count} 个XML文件")
            else:
                self.status_var.set(f"⚠️ 删除了 {deleted_count}/{total_files} 个XML文件")

        except Exception as e:
            self.status_var.set(f"❌ 删除失败: {str(e)}")
            messagebox.showerror("错误", f"删除XML文件时发生错误：{str(e)}")

    def is_ad_ed2k(self, ed2k_link: str) -> bool:
        """检查ED2K链接是否包含广告内容"""
        try:
            parts = ed2k_link.split('|')
            if len(parts) >= 3:
                filename = urllib.parse.unquote(parts[2])
                return self.is_ad_filename(filename)
        except Exception:
            return False
        return False

    def is_ad_filename(self, filename: str) -> bool:
        """检查文件名是否包含广告内容"""
        return bool(AD_PATTERN.search(filename))

def main():
    """主函数"""
    try:
        # 创建主窗口
        root = tk.Tk()

        # 设置窗口图标（如果有的话）
        try:
            # 可以在这里设置应用图标
            # root.iconbitmap("icon.ico")
            pass
        except:
            pass

        # 设置窗口属性
        root.configure(bg=ModernTheme.GLASS_BG)

        # 创建应用实例
        app = Ed2kBatchApp(root)

        # 显示启动消息
        app.status_var.set("🚀 应用已启动，准备就绪")

        # 启动主循环
        root.mainloop()

    except Exception as e:
        import traceback
        error_msg = f"应用启动失败: {str(e)}\n\n{traceback.format_exc()}"
        print(error_msg)

        # 尝试显示错误对话框
        try:
            import tkinter.messagebox as mb
            mb.showerror("启动错误", f"应用启动失败：\n{str(e)}")
        except:
            pass

if __name__ == "__main__":
    main()